/* Universal Chatbot Proxy - Options Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background: #f8f9fa;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 32px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
}

.title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* Navigation */
.nav {
    background: #f1f3f4;
    border-bottom: 1px solid #dadce0;
    padding: 0 32px;
    display: flex;
    gap: 0;
}

.nav-item {
    background: none;
    border: none;
    padding: 16px 24px;
    font-size: 14px;
    font-weight: 500;
    color: #5f6368;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.nav-item:hover {
    background: #e8eaed;
    color: #3c4043;
}

.nav-item.active {
    color: #1a73e8;
    border-bottom-color: #1a73e8;
    background: white;
}

/* Main Content */
.main {
    padding: 32px;
    min-height: 500px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.label {
    display: block;
    font-weight: 500;
    color: #3c4043;
    margin-bottom: 6px;
}

.input, .select {
    width: 100%;
    max-width: 400px;
    padding: 10px 12px;
    border: 1px solid #dadce0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    color: #3c4043;
    transition: border-color 0.2s ease;
}

.input:focus, .select:focus {
    outline: none;
    border-color: #1a73e8;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.help-text {
    font-size: 12px;
    color: #5f6368;
    margin-top: 4px;
}

/* Checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #3c4043;
}

.checkbox {
    width: 16px;
    height: 16px;
    accent-color: #1a73e8;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: #1a73e8;
    color: white;
}

.btn-primary:hover {
    background: #1557b0;
}

.btn-secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.btn-secondary:hover {
    background: #f1f3f4;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-icon {
    font-size: 16px;
}

/* About Content */
.about-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.about-item {
    margin-bottom: 8px;
    font-size: 14px;
}

.about-item strong {
    color: #2c3e50;
}

.about-item a {
    color: #1a73e8;
    text-decoration: none;
}

.about-item a:hover {
    text-decoration: underline;
}

.about-description {
    margin-bottom: 20px;
}

.about-description p {
    margin-bottom: 12px;
}

.about-description ul {
    margin-left: 20px;
    margin-bottom: 12px;
}

.about-description li {
    margin-bottom: 4px;
}

.about-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Status Bar */
.status-bar {
    background: #f1f3f4;
    border-top: 1px solid #dadce0;
    padding: 12px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-text {
    font-size: 12px;
    color: #5f6368;
}

.status-text.success {
    color: #137333;
}

.status-text.error {
    color: #d93025;
}

.status-text.info {
    color: #1a73e8;
}

.status-actions {
    display: flex;
    gap: 8px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #5f6368;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.modal-close:hover {
    background: #f1f3f4;
}

.modal-body {
    padding: 20px 24px;
}

.modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e1e8ed;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* Chatbot Cards */
.chatbot-list {
    margin-bottom: 20px;
}

.chatbot-card {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: box-shadow 0.2s ease;
}

.chatbot-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 0;
        box-shadow: none;
    }
    
    .header {
        padding: 16px 20px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .nav {
        padding: 0 20px;
        overflow-x: auto;
    }
    
    .nav-item {
        white-space: nowrap;
        padding: 12px 16px;
    }
    
    .main {
        padding: 20px;
    }
    
    .status-bar {
        padding: 12px 20px;
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
    
    .about-actions {
        flex-direction: column;
    }
    
    .about-actions .btn {
        width: 100%;
    }
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f3f4;
}

::-webkit-scrollbar-thumb {
    background: #dadce0;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #bdc1c6;
}
