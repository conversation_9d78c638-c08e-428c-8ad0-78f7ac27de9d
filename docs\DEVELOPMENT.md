# 🛠️ Development Guide

## Prerequisites

### Required Software
- **Python 3.9+** with pip
- **Node.js 16+** with npm
- **Chrome/Chromium** (latest version)
- **Git** for version control

### Recommended Tools
- **VS Code** with extensions:
  - Python
  - JavaScript/TypeScript
  - Chrome Extension Development
- **Postman** or **Insomnia** for API testing
- **Chrome DevTools** for extension debugging

## Project Setup

### 1. <PERSON>lone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/yourusername/universal-chatbot-proxy.git
cd universal-chatbot-proxy

# Install Python dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies

# Install Node.js dependencies (for extension development)
cd extension
npm install
cd ..
```

### 2. Development Environment

```bash
# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in development mode
pip install -e .
```

### 3. Environment Variables

Create a `.env` file in the project root:

```env
# Development settings
DEBUG=true
LOG_LEVEL=debug
API_PORT=11435
WEBSOCKET_PORT=11436

# Extension settings
EXTENSION_ID=your-extension-id
DEVELOPMENT_MODE=true

# Testing settings
TEST_CHATBOT_URL=https://chat.openai.com
TEST_TIMEOUT=30
```

## Development Workflow

### Running the Development Server

```bash
# Start the API server in development mode
python local_api_proxy/server.py --dev

# Or use the development script
./scripts/dev-server.sh
```

### Loading the Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `extension/` directory
5. Note the extension ID for configuration

### Hot Reloading

The development setup supports hot reloading:

- **Python**: Uses `uvicorn --reload` for automatic server restart
- **Extension**: Manual reload required (click refresh in chrome://extensions/)

## Code Structure

### Python Backend

```
local_api_proxy/
├── __init__.py
├── server.py              # Main FastAPI application
├── models.py              # Pydantic data models
├── openai_compat.py       # OpenAI API compatibility
├── bridge_client.py       # Extension communication
├── chatbot_adapters/      # Chatbot-specific adapters
│   ├── __init__.py
│   ├── base.py           # Base adapter class
│   ├── chatgpt.py        # ChatGPT adapter
│   └── kimi.py           # Kimi adapter
├── utils/                 # Utility functions
│   ├── __init__.py
│   ├── logging.py        # Logging configuration
│   └── config.py         # Configuration management
└── tests/                 # Python tests
    ├── __init__.py
    ├── test_server.py
    └── test_adapters.py
```

### Chrome Extension

```
extension/
├── manifest.json          # Extension manifest
├── background.js          # Service worker
├── contentScript.js       # Content script
├── popup/                 # Extension popup
│   ├── popup.html
│   ├── popup.js
│   └── popup.css
├── options/               # Options page
│   ├── options.html
│   ├── options.js
│   └── options.css
├── lib/                   # Shared libraries
│   ├── utils.js
│   └── adapters.js
└── tests/                 # Extension tests
    ├── test-content.js
    └── test-background.js
```

## Development Guidelines

### Python Code Style

We follow PEP 8 with some modifications:

```python
# Use type hints
def process_message(message: str, timeout: int = 30) -> dict:
    """Process a message with optional timeout."""
    pass

# Use dataclasses for simple data structures
from dataclasses import dataclass

@dataclass
class ChatMessage:
    role: str
    content: str
    timestamp: float = None
```

### JavaScript Code Style

We use modern ES6+ syntax:

```javascript
// Use async/await
async function sendMessage(message) {
    try {
        const response = await fetch('/api/message', {
            method: 'POST',
            body: JSON.stringify(message)
        });
        return await response.json();
    } catch (error) {
        console.error('Failed to send message:', error);
        throw error;
    }
}

// Use const/let instead of var
const CONFIG = {
    timeout: 30000,
    retries: 3
};
```

### Error Handling

Implement comprehensive error handling:

```python
# Python
import logging
from typing import Optional

logger = logging.getLogger(__name__)

async def safe_operation() -> Optional[dict]:
    try:
        result = await risky_operation()
        return result
    except SpecificError as e:
        logger.warning(f"Expected error: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        raise
```

```javascript
// JavaScript
class ExtensionError extends Error {
    constructor(message, code) {
        super(message);
        this.name = 'ExtensionError';
        this.code = code;
    }
}

function handleError(error) {
    if (error instanceof ExtensionError) {
        console.warn(`Extension error [${error.code}]: ${error.message}`);
    } else {
        console.error('Unexpected error:', error);
    }
}
```

## Testing

### Python Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=local_api_proxy

# Run specific test file
pytest tests/test_server.py

# Run with verbose output
pytest -v
```

### Extension Tests

```bash
cd extension
npm test

# Run specific test
npm test -- --grep "content script"

# Run with coverage
npm run test:coverage
```

### Integration Tests

```bash
# Start test environment
./scripts/test-setup.sh

# Run integration tests
python tests/integration/test_e2e.py

# Cleanup
./scripts/test-cleanup.sh
```

## Debugging

### Python Debugging

```python
# Use built-in debugger
import pdb; pdb.set_trace()

# Or use ipdb for better experience
import ipdb; ipdb.set_trace()

# VS Code debugging configuration (.vscode/launch.json)
{
    "name": "Debug Server",
    "type": "python",
    "request": "launch",
    "program": "local_api_proxy/server.py",
    "args": ["--dev"],
    "console": "integratedTerminal"
}
```

### Extension Debugging

1. **Background Script**: Open `chrome://extensions/` → Click "Inspect views: background page"
2. **Content Script**: Open DevTools on the target page → Sources tab
3. **Popup**: Right-click extension icon → "Inspect popup"

### Network Debugging

```bash
# Monitor WebSocket connections
wscat -c ws://localhost:11436

# Test API endpoints
curl -v http://localhost:11435/v1/health

# Monitor extension messages
# Add to content script:
console.log('Message sent:', message);
```

## Building and Packaging

### Development Build

```bash
# Build extension for development
cd extension
npm run build:dev

# Build Python package
python setup.py build
```

### Production Build

```bash
# Build extension for production
cd extension
npm run build:prod

# Create Python wheel
python setup.py bdist_wheel

# Create complete package
./scripts/build-release.sh
```

## Contributing

### Pull Request Process

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Push** to branch: `git push origin feature/amazing-feature`
5. **Open** a Pull Request

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Examples:
```
feat(extension): add support for Claude Web
fix(api): handle timeout errors gracefully
docs(readme): update installation instructions
```

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or properly documented)
- [ ] Performance impact considered
- [ ] Security implications reviewed

## Troubleshooting

### Common Issues

**Extension not loading**
- Check manifest.json syntax
- Verify permissions
- Check console for errors

**API server not starting**
- Check port availability: `netstat -an | grep 11435`
- Verify Python dependencies
- Check logs for errors

**WebSocket connection failed**
- Verify firewall settings
- Check browser security policies
- Test with different ports

### Getting Help

- **Issues**: GitHub Issues for bug reports
- **Discussions**: GitHub Discussions for questions
- **Discord**: Join our development Discord
- **Email**: <EMAIL>

## Performance Optimization

### Python Performance

```python
# Use async/await for I/O operations
async def fetch_data():
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()

# Use connection pooling
from aiohttp import TCPConnector
connector = TCPConnector(limit=100, limit_per_host=30)
```

### Extension Performance

```javascript
// Debounce frequent operations
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Use efficient DOM queries
const element = document.querySelector('#specific-id');
// Instead of: document.getElementsByTagName('div')
```
