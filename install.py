#!/usr/bin/env python3
"""
Universal Chatbot Proxy - Installation Script
Automated installation and setup for the Universal Chatbot Proxy
"""

import os
import sys
import subprocess
import platform
import shutil
import json
from pathlib import Path
from typing import List, Optional


class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


class Installer:
    """Main installer class."""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.extension_path = self.project_root / "extension"
        
        # Requirements
        self.min_python_version = (3, 9)
        self.required_commands = ["npm", "node"]
        
    def print_header(self):
        """Print installation header."""
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("=" * 60)
        print("    Universal Chatbot Proxy - Installation Script")
        print("=" * 60)
        print(f"{Colors.ENDC}")
        print(f"{Colors.OKCYAN}This script will install and configure the Universal Chatbot Proxy.{Colors.ENDC}")
        print()
    
    def check_requirements(self) -> bool:
        """Check system requirements."""
        print(f"{Colors.BOLD}Checking system requirements...{Colors.ENDC}")
        
        # Check Python version
        if self.python_version < self.min_python_version:
            print(f"{Colors.FAIL}❌ Python {self.min_python_version[0]}.{self.min_python_version[1]}+ required. Found {self.python_version[0]}.{self.python_version[1]}{Colors.ENDC}")
            return False
        else:
            print(f"{Colors.OKGREEN}✅ Python {self.python_version[0]}.{self.python_version[1]} detected{Colors.ENDC}")
        
        # Check required commands
        for cmd in self.required_commands:
            if not self._command_exists(cmd):
                print(f"{Colors.FAIL}❌ {cmd} not found. Please install Node.js and npm.{Colors.ENDC}")
                return False
            else:
                print(f"{Colors.OKGREEN}✅ {cmd} found{Colors.ENDC}")
        
        # Check Chrome/Chromium
        chrome_found = any(self._command_exists(browser) for browser in ["google-chrome", "chromium", "chrome"])
        if not chrome_found:
            print(f"{Colors.WARNING}⚠️  Chrome/Chromium not detected. Extension features may not work.{Colors.ENDC}")
        else:
            print(f"{Colors.OKGREEN}✅ Chrome/Chromium detected{Colors.ENDC}")
        
        print()
        return True
    
    def _command_exists(self, command: str) -> bool:
        """Check if a command exists in PATH."""
        return shutil.which(command) is not None
    
    def create_virtual_environment(self) -> bool:
        """Create Python virtual environment."""
        print(f"{Colors.BOLD}Creating Python virtual environment...{Colors.ENDC}")
        
        try:
            if self.venv_path.exists():
                print(f"{Colors.WARNING}Virtual environment already exists. Removing...{Colors.ENDC}")
                shutil.rmtree(self.venv_path)
            
            subprocess.run([sys.executable, "-m", "venv", str(self.venv_path)], check=True)
            print(f"{Colors.OKGREEN}✅ Virtual environment created{Colors.ENDC}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"{Colors.FAIL}❌ Failed to create virtual environment: {e}{Colors.ENDC}")
            return False
    
    def install_python_dependencies(self) -> bool:
        """Install Python dependencies."""
        print(f"{Colors.BOLD}Installing Python dependencies...{Colors.ENDC}")
        
        try:
            # Get pip path
            if self.system == "windows":
                pip_path = self.venv_path / "Scripts" / "pip"
            else:
                pip_path = self.venv_path / "bin" / "pip"
            
            # Upgrade pip
            subprocess.run([str(pip_path), "install", "--upgrade", "pip"], check=True)
            
            # Install requirements
            subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], check=True)
            
            # Install development requirements if requested
            if self._ask_yes_no("Install development dependencies?"):
                subprocess.run([str(pip_path), "install", "-r", "requirements-dev.txt"], check=True)
            
            print(f"{Colors.OKGREEN}✅ Python dependencies installed{Colors.ENDC}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"{Colors.FAIL}❌ Failed to install Python dependencies: {e}{Colors.ENDC}")
            return False
    
    def install_extension_dependencies(self) -> bool:
        """Install Chrome extension dependencies."""
        print(f"{Colors.BOLD}Installing Chrome extension dependencies...{Colors.ENDC}")
        
        try:
            # Change to extension directory
            original_cwd = os.getcwd()
            os.chdir(self.extension_path)
            
            # Install npm dependencies
            subprocess.run(["npm", "install"], check=True)
            
            # Build extension
            subprocess.run(["npm", "run", "build"], check=True)
            
            os.chdir(original_cwd)
            print(f"{Colors.OKGREEN}✅ Extension dependencies installed and built{Colors.ENDC}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"{Colors.FAIL}❌ Failed to install extension dependencies: {e}{Colors.ENDC}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def create_configuration(self) -> bool:
        """Create initial configuration."""
        print(f"{Colors.BOLD}Creating configuration files...{Colors.ENDC}")
        
        try:
            # Copy example environment file
            env_example = self.project_root / ".env.example"
            env_file = self.project_root / ".env"
            
            if not env_file.exists() and env_example.exists():
                shutil.copy(env_example, env_file)
                print(f"{Colors.OKGREEN}✅ Created .env file from template{Colors.ENDC}")
            
            # Create logs directory
            logs_dir = self.project_root / "logs"
            logs_dir.mkdir(exist_ok=True)
            print(f"{Colors.OKGREEN}✅ Created logs directory{Colors.ENDC}")
            
            # Create data directory
            data_dir = self.project_root / "data"
            data_dir.mkdir(exist_ok=True)
            print(f"{Colors.OKGREEN}✅ Created data directory{Colors.ENDC}")
            
            return True
            
        except Exception as e:
            print(f"{Colors.FAIL}❌ Failed to create configuration: {e}{Colors.ENDC}")
            return False
    
    def create_startup_scripts(self) -> bool:
        """Create startup scripts."""
        print(f"{Colors.BOLD}Creating startup scripts...{Colors.ENDC}")
        
        try:
            # Create start script
            if self.system == "windows":
                self._create_windows_scripts()
            else:
                self._create_unix_scripts()
            
            print(f"{Colors.OKGREEN}✅ Startup scripts created{Colors.ENDC}")
            return True
            
        except Exception as e:
            print(f"{Colors.FAIL}❌ Failed to create startup scripts: {e}{Colors.ENDC}")
            return False
    
    def _create_windows_scripts(self):
        """Create Windows batch scripts."""
        # Start script
        start_script = self.project_root / "start.bat"
        start_script.write_text(f"""@echo off
echo Starting Universal Chatbot Proxy...
cd /d "{self.project_root}"
"{self.venv_path}\\Scripts\\python.exe" -m local_api_proxy.server
pause
""")
        
        # Install script
        install_script = self.project_root / "install.bat"
        install_script.write_text(f"""@echo off
echo Installing Universal Chatbot Proxy...
cd /d "{self.project_root}"
python install.py
pause
""")
    
    def _create_unix_scripts(self):
        """Create Unix shell scripts."""
        # Start script
        start_script = self.project_root / "start.sh"
        start_script.write_text(f"""#!/bin/bash
echo "Starting Universal Chatbot Proxy..."
cd "{self.project_root}"
source venv/bin/activate
python -m local_api_proxy.server
""")
        start_script.chmod(0o755)
        
        # Install script
        install_script = self.project_root / "install.sh"
        install_script.write_text(f"""#!/bin/bash
echo "Installing Universal Chatbot Proxy..."
cd "{self.project_root}"
python3 install.py
""")
        install_script.chmod(0o755)
    
    def run_tests(self) -> bool:
        """Run basic tests to verify installation."""
        print(f"{Colors.BOLD}Running installation tests...{Colors.ENDC}")
        
        try:
            # Get python path
            if self.system == "windows":
                python_path = self.venv_path / "Scripts" / "python"
            else:
                python_path = self.venv_path / "bin" / "python"
            
            # Test import
            result = subprocess.run([
                str(python_path), "-c", 
                "import local_api_proxy; print('✅ Package import successful')"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"{Colors.OKGREEN}{result.stdout.strip()}{Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}❌ Package import failed: {result.stderr}{Colors.ENDC}")
                return False
            
            # Test configuration loading
            result = subprocess.run([
                str(python_path), "-c",
                "from local_api_proxy.config import get_config; config = get_config(); print('✅ Configuration loading successful')"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"{Colors.OKGREEN}{result.stdout.strip()}{Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}❌ Configuration loading failed: {result.stderr}{Colors.ENDC}")
                return False
            
            return True
            
        except Exception as e:
            print(f"{Colors.FAIL}❌ Tests failed: {e}{Colors.ENDC}")
            return False
    
    def print_next_steps(self):
        """Print next steps for the user."""
        print(f"\n{Colors.BOLD}{Colors.OKGREEN}🎉 Installation completed successfully!{Colors.ENDC}")
        print(f"\n{Colors.BOLD}Next steps:{Colors.ENDC}")
        print(f"1. {Colors.OKCYAN}Load the Chrome extension:{Colors.ENDC}")
        print(f"   - Open Chrome and go to chrome://extensions/")
        print(f"   - Enable 'Developer mode'")
        print(f"   - Click 'Load unpacked' and select: {self.extension_path / 'dist'}")
        print(f"\n2. {Colors.OKCYAN}Start the API server:{Colors.ENDC}")
        if self.system == "windows":
            print(f"   - Run: start.bat")
        else:
            print(f"   - Run: ./start.sh")
        print(f"   - Or: python -m local_api_proxy.server")
        print(f"\n3. {Colors.OKCYAN}Test the installation:{Colors.ENDC}")
        print(f"   - Visit: http://localhost:11435/docs")
        print(f"   - Check extension popup in Chrome")
        print(f"\n4. {Colors.OKCYAN}Configuration:{Colors.ENDC}")
        print(f"   - Edit .env file for custom settings")
        print(f"   - Check config/default.yaml for chatbot settings")
        print(f"\n{Colors.BOLD}Documentation:{Colors.ENDC}")
        print(f"   - README.md - Getting started guide")
        print(f"   - docs/API_REFERENCE.md - API documentation")
        print(f"   - docs/DEVELOPMENT.md - Development guide")
        print(f"\n{Colors.BOLD}Support:{Colors.ENDC}")
        print(f"   - GitHub: https://github.com/yourusername/universal-chatbot-proxy")
        print(f"   - Issues: https://github.com/yourusername/universal-chatbot-proxy/issues")
    
    def _ask_yes_no(self, question: str, default: bool = True) -> bool:
        """Ask a yes/no question."""
        default_str = "Y/n" if default else "y/N"
        response = input(f"{question} [{default_str}]: ").strip().lower()
        
        if not response:
            return default
        
        return response in ['y', 'yes', 'true', '1']
    
    def run(self) -> bool:
        """Run the complete installation process."""
        self.print_header()
        
        steps = [
            ("Checking requirements", self.check_requirements),
            ("Creating virtual environment", self.create_virtual_environment),
            ("Installing Python dependencies", self.install_python_dependencies),
            ("Installing extension dependencies", self.install_extension_dependencies),
            ("Creating configuration", self.create_configuration),
            ("Creating startup scripts", self.create_startup_scripts),
            ("Running tests", self.run_tests),
        ]
        
        for step_name, step_func in steps:
            print(f"{Colors.BOLD}Step: {step_name}{Colors.ENDC}")
            if not step_func():
                print(f"\n{Colors.FAIL}❌ Installation failed at step: {step_name}{Colors.ENDC}")
                return False
            print()
        
        self.print_next_steps()
        return True


def main():
    """Main entry point."""
    installer = Installer()
    
    try:
        success = installer.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}Installation cancelled by user.{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.FAIL}Unexpected error: {e}{Colors.ENDC}")
        sys.exit(1)


if __name__ == "__main__":
    main()
