<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Chatbot Proxy - Settings</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <img src="../icons/icon48.png" alt="Logo" class="logo-icon">
                    <div>
                        <h1 class="title">Universal Chatbot Proxy</h1>
                        <p class="subtitle">Configuration & Settings</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="resetBtn">Reset to Defaults</button>
                    <button class="btn btn-primary" id="saveBtn">Save Settings</button>
                </div>
            </div>
        </header>

        <nav class="nav">
            <button class="nav-item active" data-tab="general">General</button>
            <button class="nav-item" data-tab="chatbots">Chatbots</button>
            <button class="nav-item" data-tab="advanced">Advanced</button>
            <button class="nav-item" data-tab="about">About</button>
        </nav>

        <main class="main">
            <!-- General Tab -->
            <div class="tab-content active" id="general">
                <div class="section">
                    <h2 class="section-title">🔧 General Settings</h2>
                    
                    <div class="form-group">
                        <label class="label" for="apiUrl">API Server URL</label>
                        <input type="url" class="input" id="apiUrl" value="http://localhost:11435" placeholder="http://localhost:11435">
                        <p class="help-text">The URL of your local API proxy server</p>
                    </div>

                    <div class="form-group">
                        <label class="label" for="wsUrl">WebSocket URL</label>
                        <input type="url" class="input" id="wsUrl" value="ws://localhost:11436" placeholder="ws://localhost:11436">
                        <p class="help-text">WebSocket URL for real-time communication</p>
                    </div>

                    <div class="form-group">
                        <label class="label" for="defaultChatbot">Default Chatbot</label>
                        <select class="select" id="defaultChatbot">
                            <option value="chatgpt">ChatGPT</option>
                            <option value="kimi">Kimi</option>
                            <option value="claude">Claude</option>
                            <option value="custom">Custom</option>
                        </select>
                        <p class="help-text">Default chatbot to use when none is specified</p>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" class="checkbox" id="autoConnect">
                            <span class="checkmark"></span>
                            Auto-connect on startup
                        </label>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" class="checkbox" id="debugMode">
                            <span class="checkmark"></span>
                            Enable debug logging
                        </label>
                    </div>
                </div>
            </div>

            <!-- Chatbots Tab -->
            <div class="tab-content" id="chatbots">
                <div class="section">
                    <h2 class="section-title">🤖 Chatbot Configuration</h2>
                    
                    <div class="chatbot-list" id="chatbotList">
                        <!-- Chatbot configurations will be dynamically added here -->
                    </div>

                    <button class="btn btn-secondary" id="addChatbotBtn">
                        <span class="btn-icon">➕</span>
                        Add Custom Chatbot
                    </button>
                </div>
            </div>

            <!-- Advanced Tab -->
            <div class="tab-content" id="advanced">
                <div class="section">
                    <h2 class="section-title">⚙️ Advanced Settings</h2>
                    
                    <div class="form-group">
                        <label class="label" for="requestTimeout">Request Timeout (seconds)</label>
                        <input type="number" class="input" id="requestTimeout" value="30" min="5" max="300">
                        <p class="help-text">Maximum time to wait for chatbot responses</p>
                    </div>

                    <div class="form-group">
                        <label class="label" for="retryAttempts">Retry Attempts</label>
                        <input type="number" class="input" id="retryAttempts" value="3" min="1" max="10">
                        <p class="help-text">Number of retry attempts for failed requests</p>
                    </div>

                    <div class="form-group">
                        <label class="label" for="typingDelayMin">Typing Delay Min (ms)</label>
                        <input type="number" class="input" id="typingDelayMin" value="50" min="10" max="500">
                    </div>

                    <div class="form-group">
                        <label class="label" for="typingDelayMax">Typing Delay Max (ms)</label>
                        <input type="number" class="input" id="typingDelayMax" value="150" min="50" max="1000">
                    </div>

                    <div class="form-group">
                        <label class="label" for="actionDelayMin">Action Delay Min (ms)</label>
                        <input type="number" class="input" id="actionDelayMin" value="1000" min="100" max="5000">
                    </div>

                    <div class="form-group">
                        <label class="label" for="actionDelayMax">Action Delay Max (ms)</label>
                        <input type="number" class="input" id="actionDelayMax" value="3000" min="1000" max="10000">
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" class="checkbox" id="humanLikeTyping">
                            <span class="checkmark"></span>
                            Enable human-like typing simulation
                        </label>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" class="checkbox" id="randomDelays">
                            <span class="checkmark"></span>
                            Use random delays between actions
                        </label>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">🔒 Security & Privacy</h2>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" class="checkbox" id="clearDataOnClose">
                            <span class="checkmark"></span>
                            Clear conversation data when browser closes
                        </label>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" class="checkbox" id="encryptStorage">
                            <span class="checkmark"></span>
                            Encrypt stored configuration data
                        </label>
                    </div>
                </div>
            </div>

            <!-- About Tab -->
            <div class="tab-content" id="about">
                <div class="section">
                    <h2 class="section-title">ℹ️ About</h2>
                    
                    <div class="about-content">
                        <div class="about-item">
                            <strong>Version:</strong> 1.0.0
                        </div>
                        <div class="about-item">
                            <strong>Author:</strong> Universal Chatbot Proxy Team
                        </div>
                        <div class="about-item">
                            <strong>License:</strong> MIT License
                        </div>
                        <div class="about-item">
                            <strong>Repository:</strong> 
                            <a href="https://github.com/yourusername/universal-chatbot-proxy" target="_blank">
                                GitHub
                            </a>
                        </div>
                    </div>

                    <div class="about-description">
                        <p>Universal Chatbot Proxy enables you to control any web-based chatbot through OpenAI-compatible API calls. This extension works in conjunction with a local API server to provide seamless integration with your existing tools and applications.</p>
                        
                        <p><strong>Key Features:</strong></p>
                        <ul>
                            <li>OpenAI API compatibility</li>
                            <li>Support for multiple chatbot platforms</li>
                            <li>Privacy-first design (all data stays local)</li>
                            <li>Human-like interaction simulation</li>
                            <li>Real-time streaming responses</li>
                        </ul>
                    </div>

                    <div class="about-actions">
                        <button class="btn btn-secondary" id="viewDocsBtn">
                            📚 View Documentation
                        </button>
                        <button class="btn btn-secondary" id="reportIssueBtn">
                            🐛 Report Issue
                        </button>
                        <button class="btn btn-secondary" id="checkUpdatesBtn">
                            🔄 Check for Updates
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Status Bar -->
        <div class="status-bar" id="statusBar">
            <span class="status-text" id="statusText">Ready</span>
            <div class="status-actions">
                <button class="btn btn-small btn-secondary" id="testConnectionBtn">Test Connection</button>
            </div>
        </div>
    </div>

    <!-- Modal for adding custom chatbots -->
    <div class="modal" id="addChatbotModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add Custom Chatbot</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="label" for="chatbotName">Name</label>
                    <input type="text" class="input" id="chatbotName" placeholder="My Custom Chatbot">
                </div>
                <div class="form-group">
                    <label class="label" for="chatbotUrl">URL</label>
                    <input type="url" class="input" id="chatbotUrl" placeholder="https://example.com/chat">
                </div>
                <div class="form-group">
                    <label class="label" for="inputSelector">Input Selector</label>
                    <input type="text" class="input" id="inputSelector" placeholder="#chat-input">
                </div>
                <div class="form-group">
                    <label class="label" for="submitSelector">Submit Selector</label>
                    <input type="text" class="input" id="submitSelector" placeholder="#send-button">
                </div>
                <div class="form-group">
                    <label class="label" for="outputSelector">Output Selector</label>
                    <input type="text" class="input" id="outputSelector" placeholder=".response-container">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">Cancel</button>
                <button class="btn btn-primary" id="modalSave">Save Chatbot</button>
            </div>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>
