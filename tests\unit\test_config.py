"""
Universal Chatbot Proxy - Configuration Tests
Unit tests for configuration management
"""

import pytest
import tempfile
import yaml
from pathlib import Path

from local_api_proxy.config import (
    Config, ChatbotConfig, ServerConfig, WebSocketConfig,
    SecurityConfig, LoggingConfig, load_config, get_config
)


class TestChatbotConfig:
    """Test ChatbotConfig class."""
    
    def test_chatbot_config_creation(self):
        """Test creating a chatbot configuration."""
        config = ChatbotConfig(
            url="https://test.example.com",
            enabled=True,
            models=["test-model"],
            timeout=30
        )
        
        assert config.url == "https://test.example.com"
        assert config.enabled is True
        assert config.models == ["test-model"]
        assert config.timeout == 30
        assert config.max_retries == 3  # default value
    
    def test_chatbot_config_validation(self):
        """Test chatbot configuration validation."""
        # Valid URL
        config = ChatbotConfig(
            url="https://valid.example.com",
            enabled=True,
            models=["model1"]
        )
        assert config.url.startswith("https://")
        
        # Test with selectors
        config_with_selectors = ChatbotConfig(
            url="https://test.com",
            enabled=True,
            models=["model1"],
            selectors={
                "input": "#input",
                "submit": "#submit",
                "output": ".output"
            }
        )
        assert config_with_selectors.selectors["input"] == "#input"


class TestServerConfig:
    """Test ServerConfig class."""
    
    def test_server_config_defaults(self):
        """Test server configuration defaults."""
        config = ServerConfig()
        
        assert config.host == "127.0.0.1"
        assert config.port == 11435
        assert config.debug is False
        assert config.reload is False
        assert config.workers == 1
    
    def test_server_config_custom(self):
        """Test custom server configuration."""
        config = ServerConfig(
            host="0.0.0.0",
            port=8080,
            debug=True,
            workers=4
        )
        
        assert config.host == "0.0.0.0"
        assert config.port == 8080
        assert config.debug is True
        assert config.workers == 4


class TestWebSocketConfig:
    """Test WebSocketConfig class."""
    
    def test_websocket_config_defaults(self):
        """Test WebSocket configuration defaults."""
        config = WebSocketConfig()
        
        assert config.port == 11436
        assert config.ping_interval == 30
        assert config.ping_timeout == 10
    
    def test_websocket_config_custom(self):
        """Test custom WebSocket configuration."""
        config = WebSocketConfig(
            port=9999,
            ping_interval=60,
            ping_timeout=20
        )
        
        assert config.port == 9999
        assert config.ping_interval == 60
        assert config.ping_timeout == 20


class TestSecurityConfig:
    """Test SecurityConfig class."""
    
    def test_security_config_defaults(self):
        """Test security configuration defaults."""
        config = SecurityConfig()
        
        assert config.api_key_required is False
        assert config.api_keys == []
        assert config.cors_origins == ["*"]
        assert config.rate_limit_per_minute == 60
    
    def test_security_config_with_keys(self):
        """Test security configuration with API keys."""
        config = SecurityConfig(
            api_key_required=True,
            api_keys=["key1", "key2"],
            cors_origins=["https://example.com"],
            rate_limit_per_minute=100
        )
        
        assert config.api_key_required is True
        assert config.api_keys == ["key1", "key2"]
        assert config.cors_origins == ["https://example.com"]
        assert config.rate_limit_per_minute == 100


class TestLoggingConfig:
    """Test LoggingConfig class."""
    
    def test_logging_config_defaults(self):
        """Test logging configuration defaults."""
        config = LoggingConfig()
        
        assert config.level == "INFO"
        assert "%(asctime)s" in config.format
        assert config.file_path is None
        assert config.rotation == "1 day"
        assert config.retention == "30 days"
    
    def test_logging_config_custom(self):
        """Test custom logging configuration."""
        config = LoggingConfig(
            level="DEBUG",
            file_path="/var/log/app.log",
            rotation="1 hour",
            retention="7 days"
        )
        
        assert config.level == "DEBUG"
        assert config.file_path == "/var/log/app.log"
        assert config.rotation == "1 hour"
        assert config.retention == "7 days"


class TestMainConfig:
    """Test main Config class."""
    
    def test_config_creation(self):
        """Test creating main configuration."""
        config = Config()
        
        assert isinstance(config.server, ServerConfig)
        assert isinstance(config.websocket, WebSocketConfig)
        assert isinstance(config.security, SecurityConfig)
        assert isinstance(config.logging, LoggingConfig)
        assert config.default_chatbot == "chatgpt"
        assert config.default_model == "gpt-3.5-turbo"
    
    def test_config_with_chatbots(self):
        """Test configuration with chatbots."""
        chatbot_config = ChatbotConfig(
            url="https://test.com",
            enabled=True,
            models=["test-model"]
        )
        
        config = Config(
            chatbots={"test": chatbot_config}
        )
        
        assert "test" in config.chatbots
        assert config.chatbots["test"].url == "https://test.com"
    
    def test_get_chatbot_config(self):
        """Test getting chatbot configuration."""
        chatbot_config = ChatbotConfig(
            url="https://test.com",
            enabled=True,
            models=["test-model"]
        )
        
        config = Config(
            chatbots={"test": chatbot_config}
        )
        
        retrieved = config.get_chatbot_config("test")
        assert retrieved is not None
        assert retrieved.url == "https://test.com"
        
        # Test non-existent chatbot
        assert config.get_chatbot_config("nonexistent") is None
    
    def test_add_remove_chatbot(self):
        """Test adding and removing chatbots."""
        config = Config()
        
        # Add chatbot
        chatbot_config = ChatbotConfig(
            url="https://new.com",
            enabled=True,
            models=["new-model"]
        )
        config.add_chatbot("new", chatbot_config)
        
        assert "new" in config.chatbots
        assert config.chatbots["new"].url == "https://new.com"
        
        # Remove chatbot
        removed = config.remove_chatbot("new")
        assert removed is True
        assert "new" not in config.chatbots
        
        # Try to remove non-existent chatbot
        removed = config.remove_chatbot("nonexistent")
        assert removed is False


class TestConfigFileOperations:
    """Test configuration file operations."""
    
    def test_save_and_load_config(self, temp_dir):
        """Test saving and loading configuration from file."""
        config_file = temp_dir / "test_config.yaml"
        
        # Create configuration
        original_config = Config(
            server=ServerConfig(host="0.0.0.0", port=8080),
            default_chatbot="test",
            chatbots={
                "test": ChatbotConfig(
                    url="https://test.com",
                    enabled=True,
                    models=["test-model"]
                )
            }
        )
        
        # Save configuration
        original_config.save_to_file(str(config_file))
        assert config_file.exists()
        
        # Load configuration
        loaded_config = Config.load_from_file(str(config_file))
        
        assert loaded_config.server.host == "0.0.0.0"
        assert loaded_config.server.port == 8080
        assert loaded_config.default_chatbot == "test"
        assert "test" in loaded_config.chatbots
        assert loaded_config.chatbots["test"].url == "https://test.com"
    
    def test_load_nonexistent_file(self):
        """Test loading non-existent configuration file."""
        with pytest.raises(FileNotFoundError):
            Config.load_from_file("nonexistent.yaml")
    
    def test_load_invalid_yaml(self, temp_dir):
        """Test loading invalid YAML file."""
        config_file = temp_dir / "invalid.yaml"
        config_file.write_text("invalid: yaml: content: [")
        
        with pytest.raises(yaml.YAMLError):
            Config.load_from_file(str(config_file))


class TestConfigLoading:
    """Test configuration loading functions."""
    
    def test_load_config_with_path(self, temp_dir):
        """Test loading configuration with specific path."""
        config_file = temp_dir / "custom_config.yaml"
        
        # Create test configuration file
        config_data = {
            "server": {"host": "custom.host", "port": 9999},
            "default_chatbot": "custom"
        }
        
        with open(config_file, 'w') as f:
            yaml.dump(config_data, f)
        
        # Load configuration
        config = load_config(str(config_file))
        
        assert config.server.host == "custom.host"
        assert config.server.port == 9999
        assert config.default_chatbot == "custom"
    
    def test_load_config_without_path(self):
        """Test loading configuration without specific path."""
        # This should load default configuration
        config = load_config()
        
        assert isinstance(config, Config)
        assert config.server.host == "127.0.0.1"
        assert config.server.port == 11435
    
    def test_get_config_singleton(self):
        """Test get_config returns same instance."""
        config1 = get_config()
        config2 = get_config()
        
        assert config1 is config2


class TestConfigValidation:
    """Test configuration validation."""
    
    def test_valid_configuration(self):
        """Test valid configuration passes validation."""
        config = Config(
            server=ServerConfig(host="127.0.0.1", port=11435),
            websocket=WebSocketConfig(port=11436),
            security=SecurityConfig(api_key_required=False),
            chatbots={
                "test": ChatbotConfig(
                    url="https://test.com",
                    enabled=True,
                    models=["test-model"],
                    timeout=30,
                    max_retries=3
                )
            }
        )
        
        # Should not raise any exceptions
        assert config.server.port == 11435
        assert config.websocket.port == 11436
        assert "test" in config.chatbots
    
    def test_environment_variable_override(self, monkeypatch):
        """Test environment variable override."""
        # Set environment variables
        monkeypatch.setenv("API_HOST", "0.0.0.0")
        monkeypatch.setenv("API_PORT", "8080")
        monkeypatch.setenv("DEBUG", "true")
        
        # Create config (should pick up env vars)
        config = Config()
        
        # Note: This test assumes the Config class properly handles env vars
        # The actual implementation may need to be adjusted
        assert isinstance(config, Config)
