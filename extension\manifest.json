{"manifest_version": 3, "name": "Universal Chatbot Proxy", "version": "1.0.0", "description": "Control any web-based chatbot through OpenAI-compatible API calls", "permissions": ["activeTab", "storage", "scripting", "tabs", "webNavigation"], "host_permissions": ["https://chat.openai.com/*", "https://kimi.moonshot.cn/*", "https://claude.ai/*", "http://localhost:*/*", "https://*/*"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["https://chat.openai.com/*", "https://kimi.moonshot.cn/*", "https://claude.ai/*"], "js": ["lib/utils.js", "contentScript.js"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "popup/popup.html", "default_title": "Universal Chatbot Proxy", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "options_page": "options/options.html", "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["lib/*.js", "adapters/*.js", "icons/*.png"], "matches": ["https://chat.openai.com/*", "https://kimi.moonshot.cn/*", "https://claude.ai/*"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "externally_connectable": {"matches": ["http://localhost:*/*"]}}