"""
Universal Chatbot Proxy - Session Management
Session recovery, state persistence, and connection management
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import sqlite3
import aiosqlite


@dataclass
class SessionState:
    """Session state information"""
    session_id: str
    chatbot_name: str
    conversation_id: Optional[str] = None
    message_count: int = 0
    last_activity: float = None
    status: str = "active"  # active, inactive, error, recovered
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.last_activity is None:
            self.last_activity = time.time()
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ConnectionState:
    """Connection state information"""
    connection_id: str
    connection_type: str  # websocket, http, extension
    endpoint: str
    status: str = "connected"  # connected, disconnected, error, reconnecting
    last_ping: float = None
    retry_count: int = 0
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.last_ping is None:
            self.last_ping = time.time()


class SessionManager:
    """Manages sessions and their recovery"""
    
    def __init__(self, db_path: str = "sessions.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.active_sessions: Dict[str, SessionState] = {}
        self.active_connections: Dict[str, ConnectionState] = {}
        self.recovery_callbacks: Dict[str, List] = {}
        
        # Configuration
        self.session_timeout = 3600  # 1 hour
        self.cleanup_interval = 300  # 5 minutes
        self.max_retry_attempts = 5
        
        # Background tasks
        self.cleanup_task = None
        self.is_running = False
    
    async def initialize(self):
        """Initialize session manager"""
        try:
            await self._init_database()
            await self._load_sessions()
            await self._start_background_tasks()
            self.is_running = True
            self.logger.info("Session manager initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize session manager: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown session manager"""
        self.is_running = False
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self._save_sessions()
        self.logger.info("Session manager shutdown")
    
    async def _init_database(self):
        """Initialize SQLite database for session persistence"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    chatbot_name TEXT NOT NULL,
                    conversation_id TEXT,
                    message_count INTEGER DEFAULT 0,
                    last_activity REAL NOT NULL,
                    status TEXT DEFAULT 'active',
                    metadata TEXT DEFAULT '{}'
                )
            """)
            
            await db.execute("""
                CREATE TABLE IF NOT EXISTS connections (
                    connection_id TEXT PRIMARY KEY,
                    connection_type TEXT NOT NULL,
                    endpoint TEXT NOT NULL,
                    status TEXT DEFAULT 'connected',
                    last_ping REAL NOT NULL,
                    retry_count INTEGER DEFAULT 0,
                    error_message TEXT
                )
            """)
            
            await db.commit()
    
    async def _load_sessions(self):
        """Load sessions from database"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute("SELECT * FROM sessions WHERE status = 'active'") as cursor:
                    async for row in cursor:
                        session = SessionState(
                            session_id=row[0],
                            chatbot_name=row[1],
                            conversation_id=row[2],
                            message_count=row[3],
                            last_activity=row[4],
                            status=row[5],
                            metadata=json.loads(row[6])
                        )
                        self.active_sessions[session.session_id] = session
                
                async with db.execute("SELECT * FROM connections WHERE status IN ('connected', 'reconnecting')") as cursor:
                    async for row in cursor:
                        connection = ConnectionState(
                            connection_id=row[0],
                            connection_type=row[1],
                            endpoint=row[2],
                            status=row[3],
                            last_ping=row[4],
                            retry_count=row[5],
                            error_message=row[6]
                        )
                        self.active_connections[connection.connection_id] = connection
            
            self.logger.info(f"Loaded {len(self.active_sessions)} sessions and {len(self.active_connections)} connections")
            
        except Exception as e:
            self.logger.error(f"Failed to load sessions: {e}")
    
    async def _save_sessions(self):
        """Save sessions to database"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Save sessions
                for session in self.active_sessions.values():
                    await db.execute("""
                        INSERT OR REPLACE INTO sessions 
                        (session_id, chatbot_name, conversation_id, message_count, last_activity, status, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        session.session_id,
                        session.chatbot_name,
                        session.conversation_id,
                        session.message_count,
                        session.last_activity,
                        session.status,
                        json.dumps(session.metadata)
                    ))
                
                # Save connections
                for connection in self.active_connections.values():
                    await db.execute("""
                        INSERT OR REPLACE INTO connections
                        (connection_id, connection_type, endpoint, status, last_ping, retry_count, error_message)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        connection.connection_id,
                        connection.connection_type,
                        connection.endpoint,
                        connection.status,
                        connection.last_ping,
                        connection.retry_count,
                        connection.error_message
                    ))
                
                await db.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to save sessions: {e}")
    
    async def _start_background_tasks(self):
        """Start background maintenance tasks"""
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while self.is_running:
            try:
                await self._cleanup_expired_sessions()
                await self._attempt_reconnections()
                await asyncio.sleep(self.cleanup_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    async def _cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        current_time = time.time()
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            if current_time - session.last_activity > self.session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            session = self.active_sessions.pop(session_id)
            session.status = "expired"
            self.logger.info(f"Session {session_id} expired")
            
            # Execute cleanup callbacks
            await self._execute_session_callbacks(session, "expired")
    
    async def _attempt_reconnections(self):
        """Attempt to reconnect failed connections"""
        for connection in list(self.active_connections.values()):
            if connection.status == "disconnected" and connection.retry_count < self.max_retry_attempts:
                try:
                    await self._attempt_reconnection(connection)
                except Exception as e:
                    self.logger.error(f"Reconnection attempt failed for {connection.connection_id}: {e}")
    
    async def _attempt_reconnection(self, connection: ConnectionState):
        """Attempt to reconnect a specific connection"""
        connection.status = "reconnecting"
        connection.retry_count += 1
        
        self.logger.info(f"Attempting reconnection for {connection.connection_id} (attempt {connection.retry_count})")
        
        # Execute reconnection callbacks
        callbacks = self.recovery_callbacks.get(connection.connection_type, [])
        for callback in callbacks:
            try:
                success = await callback(connection)
                if success:
                    connection.status = "connected"
                    connection.retry_count = 0
                    connection.error_message = None
                    connection.last_ping = time.time()
                    self.logger.info(f"Successfully reconnected {connection.connection_id}")
                    return
            except Exception as e:
                self.logger.error(f"Reconnection callback failed: {e}")
        
        # If all callbacks failed
        if connection.retry_count >= self.max_retry_attempts:
            connection.status = "failed"
            self.logger.error(f"Connection {connection.connection_id} failed after {connection.retry_count} attempts")
    
    def create_session(self, chatbot_name: str, session_id: str = None) -> SessionState:
        """Create a new session"""
        if session_id is None:
            session_id = f"{chatbot_name}_{int(time.time())}_{len(self.active_sessions)}"
        
        session = SessionState(
            session_id=session_id,
            chatbot_name=chatbot_name
        )
        
        self.active_sessions[session_id] = session
        self.logger.info(f"Created session {session_id} for {chatbot_name}")
        
        return session
    
    def get_session(self, session_id: str) -> Optional[SessionState]:
        """Get session by ID"""
        return self.active_sessions.get(session_id)
    
    def update_session(self, session_id: str, **updates):
        """Update session state"""
        session = self.active_sessions.get(session_id)
        if session:
            for key, value in updates.items():
                if hasattr(session, key):
                    setattr(session, key, value)
            session.last_activity = time.time()
    
    def remove_session(self, session_id: str) -> bool:
        """Remove session"""
        session = self.active_sessions.pop(session_id, None)
        if session:
            session.status = "removed"
            self.logger.info(f"Removed session {session_id}")
            return True
        return False
    
    def create_connection(self, connection_type: str, endpoint: str, connection_id: str = None) -> ConnectionState:
        """Create a new connection"""
        if connection_id is None:
            connection_id = f"{connection_type}_{endpoint}_{int(time.time())}"
        
        connection = ConnectionState(
            connection_id=connection_id,
            connection_type=connection_type,
            endpoint=endpoint
        )
        
        self.active_connections[connection_id] = connection
        self.logger.info(f"Created connection {connection_id} ({connection_type} to {endpoint})")
        
        return connection
    
    def update_connection(self, connection_id: str, **updates):
        """Update connection state"""
        connection = self.active_connections.get(connection_id)
        if connection:
            for key, value in updates.items():
                if hasattr(connection, key):
                    setattr(connection, key, value)
            if "status" not in updates:
                connection.last_ping = time.time()
    
    def mark_connection_failed(self, connection_id: str, error_message: str = None):
        """Mark connection as failed"""
        connection = self.active_connections.get(connection_id)
        if connection:
            connection.status = "disconnected"
            connection.error_message = error_message
            self.logger.warning(f"Connection {connection_id} failed: {error_message}")
    
    def register_recovery_callback(self, connection_type: str, callback):
        """Register callback for connection recovery"""
        if connection_type not in self.recovery_callbacks:
            self.recovery_callbacks[connection_type] = []
        self.recovery_callbacks[connection_type].append(callback)
    
    async def _execute_session_callbacks(self, session: SessionState, event: str):
        """Execute session event callbacks"""
        # This would be implemented to notify other components of session events
        pass
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """Get session statistics"""
        active_count = len([s for s in self.active_sessions.values() if s.status == "active"])
        total_messages = sum(s.message_count for s in self.active_sessions.values())
        
        connection_stats = {}
        for conn in self.active_connections.values():
            status = conn.status
            connection_stats[status] = connection_stats.get(status, 0) + 1
        
        return {
            "total_sessions": len(self.active_sessions),
            "active_sessions": active_count,
            "total_messages": total_messages,
            "total_connections": len(self.active_connections),
            "connection_status": connection_stats,
            "uptime": time.time() - (min(s.last_activity for s in self.active_sessions.values()) if self.active_sessions else time.time())
        }


# Global session manager instance
_session_manager: Optional[SessionManager] = None


async def get_session_manager() -> SessionManager:
    """Get global session manager instance"""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
        await _session_manager.initialize()
    return _session_manager
