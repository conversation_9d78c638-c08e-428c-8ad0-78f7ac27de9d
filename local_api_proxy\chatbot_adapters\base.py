"""
Universal Chatbot Proxy - Base Chatbot Adapter
Abstract base class for chatbot adapters
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class ChatbotSelectors:
    """DOM selectors for chatbot interface"""
    input: str
    submit: str
    output: str
    loading: Optional[str] = None
    error: Optional[str] = None
    new_chat: Optional[str] = None
    clear_chat: Optional[str] = None


@dataclass
class ChatbotConfig:
    """Configuration for a chatbot adapter"""
    name: str
    url: str
    selectors: ChatbotSelectors
    models: List[str]
    timeout: int = 30
    max_retries: int = 3
    typing_delay: Dict[str, int] = None
    action_delay: Dict[str, int] = None
    
    def __post_init__(self):
        if self.typing_delay is None:
            self.typing_delay = {"min": 50, "max": 150}
        if self.action_delay is None:
            self.action_delay = {"min": 1000, "max": 3000}


class BaseChatbotAdapter(ABC):
    """Abstract base class for chatbot adapters"""
    
    def __init__(self, config: ChatbotConfig):
        self.config = config
        self.name = config.name
        self.url = config.url
        self.selectors = config.selectors
        self.models = config.models
        
        # State
        self.is_ready = False
        self.last_error = None
        self.session_id = None
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the adapter"""
        pass
    
    @abstractmethod
    async def send_message(self, message: str, options: Optional[Dict] = None) -> Dict[str, Any]:
        """Send a message to the chatbot"""
        pass
    
    @abstractmethod
    async def get_response(self, timeout: Optional[int] = None) -> Dict[str, Any]:
        """Get response from the chatbot"""
        pass
    
    @abstractmethod
    async def clear_conversation(self) -> bool:
        """Clear the current conversation"""
        pass
    
    @abstractmethod
    async def check_health(self) -> Dict[str, Any]:
        """Check adapter health status"""
        pass
    
    def get_supported_models(self) -> List[str]:
        """Get list of supported models"""
        return self.models.copy()
    
    def is_model_supported(self, model: str) -> bool:
        """Check if model is supported"""
        return model in self.models
    
    def get_status(self) -> Dict[str, Any]:
        """Get adapter status"""
        return {
            "name": self.name,
            "url": self.url,
            "ready": self.is_ready,
            "models": self.models,
            "last_error": self.last_error,
            "session_id": self.session_id
        }
    
    def _validate_selectors(self) -> bool:
        """Validate that required selectors are present"""
        required = ["input", "submit", "output"]
        for selector_name in required:
            if not getattr(self.selectors, selector_name):
                self.last_error = f"Missing required selector: {selector_name}"
                return False
        return True
    
    def _format_error_response(self, error: str, error_type: str = "adapter_error") -> Dict[str, Any]:
        """Format error response"""
        return {
            "success": False,
            "error": error,
            "error_type": error_type,
            "adapter": self.name
        }
    
    def _format_success_response(self, content: str, metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """Format success response"""
        response = {
            "success": True,
            "content": content,
            "adapter": self.name,
            "model": metadata.get("model") if metadata else None
        }
        
        if metadata:
            response["metadata"] = metadata
        
        return response


class AdapterRegistry:
    """Registry for chatbot adapters"""
    
    def __init__(self):
        self._adapters: Dict[str, type] = {}
    
    def register(self, name: str, adapter_class: type):
        """Register an adapter class"""
        if not issubclass(adapter_class, BaseChatbotAdapter):
            raise ValueError(f"Adapter must inherit from BaseChatbotAdapter")
        
        self._adapters[name] = adapter_class
    
    def get_adapter_class(self, name: str) -> Optional[type]:
        """Get adapter class by name"""
        return self._adapters.get(name)
    
    def list_adapters(self) -> List[str]:
        """List registered adapter names"""
        return list(self._adapters.keys())
    
    def create_adapter(self, name: str, config: ChatbotConfig) -> Optional[BaseChatbotAdapter]:
        """Create adapter instance"""
        adapter_class = self.get_adapter_class(name)
        if adapter_class:
            return adapter_class(config)
        return None


# Global adapter registry
adapter_registry = AdapterRegistry()
