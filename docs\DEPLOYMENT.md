# 🚀 Deployment Guide

## Overview

This guide covers different deployment scenarios for the Universal Chatbot Proxy, from local development to production-ready installations.

## Local Development Deployment

### Quick Start

```bash
# 1. Clone and setup
git clone https://github.com/yourusername/universal-chatbot-proxy.git
cd universal-chatbot-proxy
pip install -r requirements.txt

# 2. Start the server
python local_api_proxy/server.py

# 3. Load extension in Chrome
# Navigate to chrome://extensions/
# Enable Developer mode
# Click "Load unpacked" and select extension/ folder
```

### Development Configuration

Create `config/development.yaml`:

```yaml
server:
  host: "127.0.0.1"
  port: 11435
  debug: true
  reload: true

websocket:
  port: 11436
  ping_interval: 30

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

chatbots:
  chatgpt:
    url: "https://chat.openai.com"
    enabled: true
    timeout: 30
  kimi:
    url: "https://kimi.moonshot.cn"
    enabled: true
    timeout: 30
```

## Production Deployment

### System Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 10GB
- OS: Windows 10+, macOS 10.15+, Ubuntu 18.04+

**Recommended Requirements:**
- CPU: 4 cores
- RAM: 8GB
- Storage: 20GB SSD
- Network: Stable internet connection

### Installation Methods

#### Method 1: Standalone Executable

```bash
# Download pre-built executable
wget https://github.com/yourusername/universal-chatbot-proxy/releases/latest/download/universal-chatbot-proxy-windows.exe

# Or for Linux/macOS
wget https://github.com/yourusername/universal-chatbot-proxy/releases/latest/download/universal-chatbot-proxy-linux
chmod +x universal-chatbot-proxy-linux

# Run the executable
./universal-chatbot-proxy-linux --config production.yaml
```

#### Method 2: Python Installation

```bash
# Install from PyPI
pip install universal-chatbot-proxy

# Or install from source
git clone https://github.com/yourusername/universal-chatbot-proxy.git
cd universal-chatbot-proxy
pip install .

# Run the server
universal-chatbot-proxy --config production.yaml
```

#### Method 3: Docker Deployment

```bash
# Pull the image
docker pull universalchatbotproxy/proxy:latest

# Run with default configuration
docker run -d \
  --name chatbot-proxy \
  -p 11435:11435 \
  -p 11436:11436 \
  universalchatbotproxy/proxy:latest

# Run with custom configuration
docker run -d \
  --name chatbot-proxy \
  -p 11435:11435 \
  -p 11436:11436 \
  -v $(pwd)/config:/app/config \
  universalchatbotproxy/proxy:latest
```

### Production Configuration

Create `config/production.yaml`:

```yaml
server:
  host: "0.0.0.0"  # Listen on all interfaces
  port: 11435
  debug: false
  workers: 4

security:
  cors_origins: ["http://localhost:3000", "https://yourdomain.com"]
  rate_limit: 100  # requests per minute
  api_key_required: false  # Set to true for API key authentication

logging:
  level: "INFO"
  file: "/var/log/chatbot-proxy/app.log"
  rotation: "1 day"
  retention: "30 days"

performance:
  connection_pool_size: 50
  timeout: 60
  max_concurrent_requests: 100

monitoring:
  metrics_enabled: true
  health_check_interval: 30
  prometheus_port: 9090
```

## Chrome Extension Deployment

### Development Installation

1. **Load Unpacked Extension**
   ```bash
   # Navigate to chrome://extensions/
   # Enable "Developer mode"
   # Click "Load unpacked"
   # Select the extension/ directory
   ```

2. **Extension Configuration**
   - Click the extension icon
   - Configure chatbot URLs
   - Set up model mappings
   - Test connection to local API

### Production Installation

#### Chrome Web Store (Recommended)

1. **Package Extension**
   ```bash
   cd extension
   npm run build:prod
   zip -r extension.zip dist/
   ```

2. **Submit to Chrome Web Store**
   - Create developer account
   - Upload extension package
   - Complete store listing
   - Submit for review

#### Enterprise Deployment

For organizations deploying to multiple users:

```json
// enterprise_policy.json
{
  "ExtensionInstallForcelist": [
    "your-extension-id;https://clients2.google.com/service/update2/crx"
  ],
  "ExtensionSettings": {
    "your-extension-id": {
      "installation_mode": "force_installed",
      "update_url": "https://clients2.google.com/service/update2/crx"
    }
  }
}
```

## Service Management

### Windows Service

```powershell
# Install as Windows service using NSSM
nssm install "Universal Chatbot Proxy" "C:\path\to\universal-chatbot-proxy.exe"
nssm set "Universal Chatbot Proxy" AppParameters "--config C:\path\to\production.yaml"
nssm set "Universal Chatbot Proxy" AppDirectory "C:\path\to\app"
nssm start "Universal Chatbot Proxy"
```

### Linux Systemd Service

Create `/etc/systemd/system/chatbot-proxy.service`:

```ini
[Unit]
Description=Universal Chatbot Proxy
After=network.target

[Service]
Type=simple
User=chatbot-proxy
WorkingDirectory=/opt/chatbot-proxy
ExecStart=/opt/chatbot-proxy/bin/universal-chatbot-proxy --config /etc/chatbot-proxy/production.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable chatbot-proxy
sudo systemctl start chatbot-proxy
sudo systemctl status chatbot-proxy
```

### macOS LaunchDaemon

Create `/Library/LaunchDaemons/com.universalchatbotproxy.plist`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.universalchatbotproxy</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/universal-chatbot-proxy</string>
        <string>--config</string>
        <string>/usr/local/etc/chatbot-proxy/production.yaml</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
</dict>
</plist>
```

```bash
# Load and start service
sudo launchctl load /Library/LaunchDaemons/com.universalchatbotproxy.plist
sudo launchctl start com.universalchatbotproxy
```

## Monitoring and Logging

### Health Monitoring

```bash
# Check service health
curl http://localhost:11435/v1/health

# Monitor logs
tail -f /var/log/chatbot-proxy/app.log

# Check system resources
htop
```

### Prometheus Metrics

Enable metrics in configuration:

```yaml
monitoring:
  metrics_enabled: true
  prometheus_port: 9090
```

Example metrics:
- `chatbot_proxy_requests_total`
- `chatbot_proxy_request_duration_seconds`
- `chatbot_proxy_active_connections`
- `chatbot_proxy_errors_total`

### Log Aggregation

For centralized logging with ELK stack:

```yaml
logging:
  handlers:
    - type: "file"
      filename: "/var/log/chatbot-proxy/app.log"
    - type: "elasticsearch"
      host: "elasticsearch.example.com"
      index: "chatbot-proxy-logs"
```

## Security Considerations

### Network Security

```yaml
security:
  # Bind to localhost only for single-user deployments
  bind_address: "127.0.0.1"
  
  # Use HTTPS in production
  ssl_cert: "/path/to/cert.pem"
  ssl_key: "/path/to/key.pem"
  
  # Enable CORS for web applications
  cors_origins: ["https://yourdomain.com"]
  
  # Rate limiting
  rate_limit: 60  # requests per minute
```

### API Security

```yaml
authentication:
  # Require API keys
  api_key_required: true
  api_keys:
    - "your-secure-api-key-here"
  
  # JWT authentication (optional)
  jwt_secret: "your-jwt-secret"
  jwt_expiry: 3600  # 1 hour
```

### Extension Security

- Use Content Security Policy
- Validate all inputs
- Sanitize DOM interactions
- Implement permission-based access

## Backup and Recovery

### Configuration Backup

```bash
# Backup configuration
tar -czf config-backup-$(date +%Y%m%d).tar.gz config/

# Backup user data
cp -r ~/.chatbot-proxy chatbot-proxy-backup/
```

### Database Backup (if applicable)

```bash
# SQLite backup
sqlite3 chatbot-proxy.db ".backup backup.db"

# PostgreSQL backup
pg_dump chatbot_proxy > backup.sql
```

### Recovery Procedures

1. **Service Recovery**
   ```bash
   # Stop service
   sudo systemctl stop chatbot-proxy
   
   # Restore configuration
   tar -xzf config-backup.tar.gz
   
   # Restart service
   sudo systemctl start chatbot-proxy
   ```

2. **Extension Recovery**
   - Reinstall extension from Chrome Web Store
   - Import configuration from backup
   - Verify connection to API server

## Performance Tuning

### Server Optimization

```yaml
performance:
  # Increase worker processes
  workers: 8
  
  # Optimize connection pooling
  connection_pool_size: 100
  max_connections: 1000
  
  # Adjust timeouts
  request_timeout: 30
  keepalive_timeout: 65
  
  # Enable compression
  gzip_compression: true
```

### System Optimization

```bash
# Increase file descriptor limits
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# Optimize network settings
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p
```

## Troubleshooting

### Common Issues

**Service won't start**
```bash
# Check logs
journalctl -u chatbot-proxy -f

# Verify configuration
universal-chatbot-proxy --config production.yaml --validate

# Check port availability
netstat -tulpn | grep 11435
```

**Extension connection issues**
- Verify API server is running
- Check firewall settings
- Test with curl: `curl http://localhost:11435/v1/health`

**Performance issues**
- Monitor resource usage: `htop`
- Check logs for errors
- Increase worker processes
- Optimize configuration

### Support

For deployment issues:
- Check [Troubleshooting Guide](TROUBLESHOOTING.md)
- Open GitHub issue with logs
- Contact support: <EMAIL>
