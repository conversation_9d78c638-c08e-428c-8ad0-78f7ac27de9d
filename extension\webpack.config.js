const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  return {
    entry: {
      background: './src/background.js',
      contentScript: './src/contentScript.js',
      popup: './src/popup/popup.js',
      options: './src/options/options.js'
    },
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      clean: true
    },
    
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env']
            }
          }
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    
    plugins: [
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'manifest.json',
            to: 'manifest.json'
          },
          {
            from: 'lib',
            to: 'lib'
          },
          {
            from: 'popup',
            to: 'popup',
            globOptions: {
              ignore: ['**/*.js']
            }
          },
          {
            from: 'options',
            to: 'options',
            globOptions: {
              ignore: ['**/*.js']
            }
          },
          {
            from: 'icons',
            to: 'icons'
          }
        ]
      })
    ],
    
    devtool: isProduction ? false : 'source-map',
    
    optimization: {
      minimize: isProduction
    },
    
    resolve: {
      extensions: ['.js', '.json']
    }
  };
};
