"""
Universal Chatbot Proxy - Logging Configuration
Centralized logging setup and configuration
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from ..config import LoggingConfig


def setup_logging(config: LoggingConfig) -> None:
    """Setup logging configuration"""
    
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, config.level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(config.format)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (if specified)
    if config.file_path:
        file_path = Path(config.file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Use rotating file handler
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=file_path,
            when='D',  # Daily rotation
            interval=1,
            backupCount=30,  # Keep 30 days of logs
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("websockets").setLevel(logging.WARNING)
    
    logger.info("Logging configured successfully")


class StructuredLogger:
    """Structured logger with context support"""
    
    def __init__(self, name: str, context: Optional[dict] = None):
        self.logger = logging.getLogger(name)
        self.context = context or {}
    
    def _format_message(self, message: str, extra: Optional[dict] = None) -> str:
        """Format message with context"""
        ctx = {**self.context, **(extra or {})}
        if ctx:
            ctx_str = " ".join(f"{k}={v}" for k, v in ctx.items())
            return f"{message} [{ctx_str}]"
        return message
    
    def debug(self, message: str, **kwargs):
        self.logger.debug(self._format_message(message, kwargs))
    
    def info(self, message: str, **kwargs):
        self.logger.info(self._format_message(message, kwargs))
    
    def warning(self, message: str, **kwargs):
        self.logger.warning(self._format_message(message, kwargs))
    
    def error(self, message: str, **kwargs):
        self.logger.error(self._format_message(message, kwargs))
    
    def critical(self, message: str, **kwargs):
        self.logger.critical(self._format_message(message, kwargs))
    
    def with_context(self, **context) -> "StructuredLogger":
        """Create new logger with additional context"""
        new_context = {**self.context, **context}
        return StructuredLogger(self.logger.name, new_context)
