# 📚 API Reference

## Base URL
```
http://localhost:11435/v1
```

## Authentication
The proxy doesn't require real authentication, but you must provide an API key for OpenAI library compatibility:
```python
openai.api_key = "dummy-key"  # Any non-empty string
```

## Endpoints

### Chat Completions
Create a chat completion using any configured chatbot.

**Endpoint**: `POST /v1/chat/completions`

#### Request Body
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user", 
      "content": "Hello!"
    }
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 1000,
  "top_p": 1.0,
  "frequency_penalty": 0.0,
  "presence_penalty": 0.0
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `model` | string | Yes | Model identifier (mapped to chatbot) |
| `messages` | array | Yes | Array of message objects |
| `stream` | boolean | No | Enable streaming responses (default: false) |
| `temperature` | number | No | Sampling temperature 0-2 (default: 0.7) |
| `max_tokens` | integer | No | Maximum tokens in response |
| `top_p` | number | No | Nucleus sampling parameter |
| `frequency_penalty` | number | No | Frequency penalty -2 to 2 |
| `presence_penalty` | number | No | Presence penalty -2 to 2 |

#### Response (Non-streaming)
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 9,
    "total_tokens": 19
  }
}
```

#### Response (Streaming)
```json
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

### Legacy Completions
Create a text completion (legacy endpoint).

**Endpoint**: `POST /v1/completions`

#### Request Body
```json
{
  "model": "text-davinci-003",
  "prompt": "Say hello",
  "max_tokens": 100,
  "temperature": 0.7,
  "stream": false
}
```

#### Response
```json
{
  "id": "cmpl-123",
  "object": "text_completion",
  "created": 1677652288,
  "model": "text-davinci-003",
  "choices": [
    {
      "text": "Hello! How are you doing today?",
      "index": 0,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 2,
    "completion_tokens": 8,
    "total_tokens": 10
  }
}
```

### Models
List available models (chatbot configurations).

**Endpoint**: `GET /v1/models`

#### Response
```json
{
  "object": "list",
  "data": [
    {
      "id": "gpt-3.5-turbo",
      "object": "model",
      "created": 1677610602,
      "owned_by": "openai",
      "permission": [],
      "root": "gpt-3.5-turbo",
      "parent": null
    },
    {
      "id": "gpt-4",
      "object": "model", 
      "created": 1677610602,
      "owned_by": "openai",
      "permission": [],
      "root": "gpt-4",
      "parent": null
    }
  ]
}
```

## Model Mapping

Configure which chatbot handles each model name:

| Model Name | Default Chatbot | Configurable |
|------------|-----------------|--------------|
| `gpt-3.5-turbo` | ChatGPT | ✅ |
| `gpt-4` | ChatGPT | ✅ |
| `kimi` | Kimi | ✅ |
| `claude-3` | Claude Web | ✅ |
| `custom-*` | User-defined | ✅ |

## Error Responses

### Standard Error Format
```json
{
  "error": {
    "message": "Error description",
    "type": "invalid_request_error",
    "param": "parameter_name",
    "code": "error_code"
  }
}
```

### Common Error Codes

| HTTP Status | Error Type | Description |
|-------------|------------|-------------|
| 400 | `invalid_request_error` | Malformed request |
| 401 | `authentication_error` | Invalid API key |
| 429 | `rate_limit_exceeded` | Too many requests |
| 500 | `api_error` | Internal server error |
| 503 | `service_unavailable` | Chatbot unavailable |

## Usage Examples

### Python (OpenAI Library)
```python
import openai

openai.api_base = "http://localhost:11435/v1"
openai.api_key = "dummy-key"

# Chat completion
response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello!"}]
)

# Streaming
for chunk in openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Tell me a story"}],
    stream=True
):
    print(chunk.choices[0].delta.get("content", ""), end="")
```

### JavaScript (Node.js)
```javascript
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: "dummy-key",
  basePath: "http://localhost:11435/v1"
});

const openai = new OpenAIApi(configuration);

async function chatCompletion() {
  const response = await openai.createChatCompletion({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: "Hello!" }]
  });
  
  console.log(response.data.choices[0].message.content);
}
```

### cURL
```bash
# Chat completion
curl -X POST http://localhost:11435/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dummy-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'

# Streaming
curl -X POST http://localhost:11435/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dummy-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": true
  }'
```

## Configuration API

### Get Configuration
**Endpoint**: `GET /v1/config`

```json
{
  "chatbots": {
    "chatgpt": {
      "url": "https://chat.openai.com",
      "models": ["gpt-3.5-turbo", "gpt-4"],
      "enabled": true
    },
    "kimi": {
      "url": "https://kimi.moonshot.cn",
      "models": ["kimi"],
      "enabled": true
    }
  },
  "default_model": "gpt-3.5-turbo",
  "timeout": 30000,
  "retry_attempts": 3
}
```

### Update Configuration
**Endpoint**: `PUT /v1/config`

```json
{
  "chatbots": {
    "custom": {
      "url": "https://example.com/chat",
      "models": ["custom-model"],
      "selectors": {
        "input": "#chat-input",
        "submit": "#send-button",
        "output": ".response-container"
      },
      "enabled": true
    }
  }
}
```

## Health Check

### Server Status
**Endpoint**: `GET /v1/health`

```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 3600,
  "extension_connected": true,
  "active_chatbots": ["chatgpt", "kimi"],
  "last_request": "2024-01-01T12:00:00Z"
}
```

## Rate Limiting

The proxy implements rate limiting to prevent abuse:

- **Default**: 60 requests per minute per client
- **Burst**: Up to 10 requests in 10 seconds
- **Headers**: Rate limit info in response headers

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```
