"""
Universal Chatbot Proxy - Kimi Adapter
Adapter for Kimi.moonshot.cn interface
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any

from .base import BaseChatbot<PERSON>dapter, ChatbotConfig, ChatbotSelectors, adapter_registry


class KimiAdapter(BaseChatbotAdapter):
    """Adapter for Kimi.moonshot.cn interface"""
    
    def __init__(self, config: Optional[ChatbotConfig] = None):
        if config is None:
            config = self._get_default_config()
        
        super().__init__(config)
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # Kimi-specific state
        self.chat_id = None
        self.message_count = 0
        self.last_response_time = None
        
        # Response tracking
        self.waiting_for_response = False
        self.current_response = ""
        self.response_complete = False
        
        # Kimi-specific features
        self.supports_file_upload = True
        self.supports_web_search = True
        self.max_context_length = 128000  # <PERSON><PERSON> supports long context
    
    @classmethod
    def _get_default_config(cls) -> ChatbotConfig:
        """Get default Kimi configuration"""
        selectors = ChatbotSelectors(
            input='.chat-input textarea',
            submit='.send-button',
            output='.message-content',
            loading='.typing-indicator',
            error='.error-message',
            new_chat='button[data-testid="new-chat"]',
            clear_chat='button[data-testid="clear-chat"]'
        )
        
        return ChatbotConfig(
            name="kimi",
            url="https://kimi.moonshot.cn",
            selectors=selectors,
            models=["kimi", "moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
            timeout=60,
            max_retries=3,
            typing_delay={"min": 30, "max": 100},  # Kimi is more sensitive to fast typing
            action_delay={"min": 800, "max": 2000}
        )
    
    async def initialize(self) -> bool:
        """Initialize the Kimi adapter"""
        try:
            self.logger.info("Initializing Kimi adapter")
            
            # Validate selectors
            if not self._validate_selectors():
                return False
            
            # Check if we can access the page
            health_check = await self.check_health()
            if not health_check.get("healthy", False):
                self.last_error = "Kimi page not accessible"
                return False
            
            self.is_ready = True
            self.logger.info("Kimi adapter initialized successfully")
            return True
            
        except Exception as e:
            self.last_error = f"Initialization failed: {str(e)}"
            self.logger.error(self.last_error)
            return False
    
    async def send_message(self, message: str, options: Optional[Dict] = None) -> Dict[str, Any]:
        """Send a message to Kimi"""
        if not self.is_ready:
            return self._format_error_response("Adapter not ready")
        
        options = options or {}
        timeout = options.get("timeout", self.config.timeout)
        
        try:
            self.logger.info(f"Sending message to Kimi: {message[:100]}...")
            
            # Reset response state
            self.waiting_for_response = True
            self.current_response = ""
            self.response_complete = False
            
            # Check for special Kimi features
            enhanced_options = self._enhance_options_for_kimi(options)
            
            # Send the message via extension
            send_result = await self._send_message_to_extension(message, enhanced_options)
            
            if not send_result.get("success", False):
                return self._format_error_response(
                    send_result.get("error", "Failed to send message")
                )
            
            # Wait for response
            response = await self._wait_for_response(timeout)
            
            if response.get("success", False):
                self.message_count += 1
                self.last_response_time = time.time()
            
            return response
            
        except Exception as e:
            self.last_error = str(e)
            self.logger.error(f"Error sending message: {e}")
            return self._format_error_response(str(e))
        finally:
            self.waiting_for_response = False
    
    async def get_response(self, timeout: Optional[int] = None) -> Dict[str, Any]:
        """Get the latest response from Kimi"""
        timeout = timeout or self.config.timeout
        
        try:
            if self.waiting_for_response:
                return await self._wait_for_response(timeout)
            else:
                # Get the last response from the page
                return await self._extract_last_response()
                
        except Exception as e:
            self.logger.error(f"Error getting response: {e}")
            return self._format_error_response(str(e))
    
    async def clear_conversation(self) -> bool:
        """Clear the current Kimi conversation"""
        try:
            self.logger.info("Clearing Kimi conversation")
            
            # Send clear command to extension
            result = await self._send_command_to_extension("clear_conversation")
            
            if result.get("success", False):
                self.chat_id = None
                self.message_count = 0
                self.current_response = ""
                self.response_complete = False
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error clearing conversation: {e}")
            return False
    
    async def check_health(self) -> Dict[str, Any]:
        """Check Kimi adapter health"""
        try:
            # Send health check to extension
            result = await self._send_command_to_extension("health_check")
            
            healthy = result.get("success", False)
            
            return {
                "healthy": healthy,
                "url": self.url,
                "selectors_valid": self._validate_selectors(),
                "message_count": self.message_count,
                "last_response_time": self.last_response_time,
                "chat_id": self.chat_id,
                "supports_file_upload": self.supports_file_upload,
                "supports_web_search": self.supports_web_search,
                "max_context_length": self.max_context_length,
                "error": result.get("error") if not healthy else None
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }
    
    def _enhance_options_for_kimi(self, options: Dict) -> Dict:
        """Enhance options with Kimi-specific features"""
        enhanced = options.copy()
        
        # Add Kimi-specific selectors and behaviors
        enhanced.update({
            "kimi_specific": {
                "wait_for_thinking": True,  # Kimi shows thinking process
                "handle_web_search": self.supports_web_search,
                "handle_file_references": self.supports_file_upload,
                "context_length": self.max_context_length
            },
            "selectors": {
                "input": self.selectors.input,
                "submit": self.selectors.submit,
                "output": self.selectors.output,
                "loading": self.selectors.loading,
                "thinking": '.thinking-indicator',  # Kimi-specific
                "web_search": '.web-search-indicator',  # Kimi-specific
                "file_upload": '.file-upload-area'  # Kimi-specific
            }
        })
        
        return enhanced
    
    async def _send_message_to_extension(self, message: str, options: Dict) -> Dict[str, Any]:
        """Send message to Kimi via extension"""
        command = {
            "action": "send_message",
            "chatbot": "kimi",
            "message": message,
            "options": {
                **options,
                "typing_delay": self.config.typing_delay,
                "action_delay": self.config.action_delay
            }
        }
        
        # TODO: Implement actual extension communication
        # This is a placeholder that would be replaced with real WebSocket communication
        
        return {"success": True, "message_sent": True}
    
    async def _send_command_to_extension(self, command: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Send command to extension"""
        command_data = {
            "action": command,
            "chatbot": "kimi",
            "params": params or {}
        }
        
        # TODO: Implement actual extension communication
        return {"success": True, "command": command}
    
    async def _wait_for_response(self, timeout: int) -> Dict[str, Any]:
        """Wait for response from Kimi"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.response_complete:
                return self._format_success_response(
                    self.current_response,
                    {
                        "model": "kimi",
                        "chat_id": self.chat_id,
                        "message_count": self.message_count,
                        "response_time": time.time() - start_time,
                        "supports_web_search": self.supports_web_search,
                        "supports_file_upload": self.supports_file_upload
                    }
                )
            
            # Check for response updates from extension
            await asyncio.sleep(0.5)
        
        return self._format_error_response("Response timeout", "timeout")
    
    async def _extract_last_response(self) -> Dict[str, Any]:
        """Extract the last response from the page"""
        try:
            # Send command to extension to extract last response
            result = await self._send_command_to_extension("extract_response")
            
            if result.get("success", False):
                content = result.get("content", "")
                return self._format_success_response(
                    content,
                    {
                        "model": "kimi",
                        "extracted": True,
                        "chat_id": self.chat_id,
                        "web_search_used": result.get("web_search_used", False),
                        "files_referenced": result.get("files_referenced", [])
                    }
                )
            
            return self._format_error_response("Failed to extract response")
            
        except Exception as e:
            return self._format_error_response(str(e))
    
    def update_response(self, content: str, complete: bool = False, metadata: Optional[Dict] = None):
        """Update response content (called by extension bridge)"""
        self.current_response = content
        self.response_complete = complete
        
        # Handle Kimi-specific metadata
        if metadata:
            if "web_search_used" in metadata:
                self.logger.info("Kimi used web search for this response")
            if "files_referenced" in metadata:
                self.logger.info(f"Kimi referenced files: {metadata['files_referenced']}")
        
        if complete:
            self.waiting_for_response = False
    
    def set_chat_id(self, chat_id: str):
        """Set chat ID"""
        self.chat_id = chat_id
    
    async def upload_file(self, file_path: str, file_type: str = "auto") -> Dict[str, Any]:
        """Upload file to Kimi (Kimi-specific feature)"""
        try:
            self.logger.info(f"Uploading file to Kimi: {file_path}")
            
            result = await self._send_command_to_extension("upload_file", {
                "file_path": file_path,
                "file_type": file_type
            })
            
            return result
            
        except Exception as e:
            return self._format_error_response(f"File upload failed: {str(e)}")
    
    async def enable_web_search(self, enabled: bool = True) -> Dict[str, Any]:
        """Enable/disable web search for Kimi"""
        try:
            result = await self._send_command_to_extension("set_web_search", {
                "enabled": enabled
            })
            
            if result.get("success", False):
                self.supports_web_search = enabled
            
            return result
            
        except Exception as e:
            return self._format_error_response(f"Web search toggle failed: {str(e)}")
    
    def get_kimi_specific_status(self) -> Dict[str, Any]:
        """Get Kimi-specific status information"""
        return {
            **self.get_status(),
            "chat_id": self.chat_id,
            "message_count": self.message_count,
            "last_response_time": self.last_response_time,
            "waiting_for_response": self.waiting_for_response,
            "response_complete": self.response_complete,
            "current_response_length": len(self.current_response),
            "supports_file_upload": self.supports_file_upload,
            "supports_web_search": self.supports_web_search,
            "max_context_length": self.max_context_length
        }


# Register the adapter
adapter_registry.register("kimi", KimiAdapter)
