"""
Universal Chatbot Proxy - Extension Bridge Client
Handles WebSocket communication with Chrome extension
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Optional, AsyncGenerator, Callable, Any
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from .config import Config
from .models import ExtensionMessage, ExtensionResponse


class ExtensionBridge:
    """WebSocket bridge for communicating with Chrome extension"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Connection state
        self.websocket = None
        self.is_running = False
        self.connection_attempts = 0
        self.max_connection_attempts = 10
        self.reconnect_delay = 5.0
        
        # Message handling
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.streaming_requests: Dict[str, asyncio.Queue] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        # Statistics
        self.messages_sent = 0
        self.messages_received = 0
        self.connection_start_time = None
        
        self._setup_message_handlers()
    
    def _setup_message_handlers(self):
        """Setup message type handlers"""
        self.message_handlers = {
            'response': self._handle_response,
            'stream_chunk': self._handle_stream_chunk,
            'stream_end': self._handle_stream_end,
            'error': self._handle_error,
            'ping': self._handle_ping,
            'status_update': self._handle_status_update
        }
    
    async def start(self):
        """Start the bridge connection"""
        self.is_running = True
        self.logger.info("Starting extension bridge...")
        
        # Start connection task
        asyncio.create_task(self._connection_manager())
    
    async def stop(self):
        """Stop the bridge connection"""
        self.is_running = False
        self.logger.info("Stopping extension bridge...")
        
        # Cancel pending requests
        for future in self.pending_requests.values():
            if not future.done():
                future.cancel()
        
        # Close WebSocket connection
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
    
    async def _connection_manager(self):
        """Manage WebSocket connection with auto-reconnect"""
        while self.is_running:
            try:
                await self._connect()
                self.connection_attempts = 0
                await self._handle_messages()
                
            except Exception as e:
                self.logger.error(f"Connection error: {e}")
                self.connection_attempts += 1
                
                if self.connection_attempts >= self.max_connection_attempts:
                    self.logger.error("Max connection attempts reached, stopping bridge")
                    break
                
                # Wait before reconnecting
                await asyncio.sleep(self.reconnect_delay)
                self.reconnect_delay = min(self.reconnect_delay * 1.5, 60.0)
    
    async def _connect(self):
        """Establish WebSocket connection"""
        ws_url = self.config.websocket.port
        if isinstance(ws_url, int):
            ws_url = f"ws://localhost:{ws_url}"
        
        self.logger.info(f"Connecting to extension at {ws_url}")
        
        try:
            self.websocket = await websockets.connect(
                ws_url,
                ping_interval=self.config.websocket.ping_interval,
                ping_timeout=self.config.websocket.ping_timeout,
                close_timeout=10
            )
            
            self.connection_start_time = time.time()
            self.reconnect_delay = 5.0  # Reset delay on successful connection
            
            self.logger.info("Connected to extension successfully")
            
            # Send initial handshake
            await self._send_handshake()
            
        except Exception as e:
            self.logger.error(f"Failed to connect to extension: {e}")
            raise
    
    async def _send_handshake(self):
        """Send initial handshake message"""
        handshake = {
            "type": "handshake",
            "payload": {
                "version": "1.0.0",
                "capabilities": ["chat_completion", "streaming", "multiple_chatbots"],
                "config": {
                    "chatbots": list(self.config.chatbots.keys()),
                    "default_chatbot": self.config.default_chatbot
                }
            }
        }
        
        await self._send_raw_message(handshake)
    
    async def _handle_messages(self):
        """Handle incoming WebSocket messages"""
        try:
            async for message in self.websocket:
                await self._process_message(message)
                
        except ConnectionClosed:
            self.logger.warning("WebSocket connection closed")
            self.websocket = None
        except WebSocketException as e:
            self.logger.error(f"WebSocket error: {e}")
            self.websocket = None
        except Exception as e:
            self.logger.error(f"Message handling error: {e}")
            self.websocket = None
    
    async def _process_message(self, raw_message: str):
        """Process incoming message"""
        try:
            message = json.loads(raw_message)
            self.messages_received += 1
            
            message_type = message.get('type')
            message_id = message.get('id')
            
            self.logger.debug(f"Received message: {message_type} (id: {message_id})")
            
            # Handle message based on type
            handler = self.message_handlers.get(message_type)
            if handler:
                await handler(message)
            else:
                self.logger.warning(f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse message: {e}")
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
    
    async def _handle_response(self, message: Dict):
        """Handle response message"""
        message_id = message.get('id')
        if message_id in self.pending_requests:
            future = self.pending_requests.pop(message_id)
            if not future.done():
                future.set_result(message.get('payload', {}))
    
    async def _handle_stream_chunk(self, message: Dict):
        """Handle streaming chunk"""
        message_id = message.get('id')
        if message_id in self.streaming_requests:
            queue = self.streaming_requests[message_id]
            await queue.put(message.get('payload', {}))
    
    async def _handle_stream_end(self, message: Dict):
        """Handle end of stream"""
        message_id = message.get('id')
        if message_id in self.streaming_requests:
            queue = self.streaming_requests[message_id]
            await queue.put(None)  # Signal end of stream
    
    async def _handle_error(self, message: Dict):
        """Handle error message"""
        message_id = message.get('id')
        error_msg = message.get('payload', {}).get('error', 'Unknown error')
        
        if message_id in self.pending_requests:
            future = self.pending_requests.pop(message_id)
            if not future.done():
                future.set_exception(Exception(error_msg))
        
        if message_id in self.streaming_requests:
            queue = self.streaming_requests[message_id]
            await queue.put(Exception(error_msg))
    
    async def _handle_ping(self, message: Dict):
        """Handle ping message"""
        pong = {
            "type": "pong",
            "timestamp": time.time()
        }
        await self._send_raw_message(pong)
    
    async def _handle_status_update(self, message: Dict):
        """Handle status update from extension"""
        payload = message.get('payload', {})
        self.logger.debug(f"Extension status update: {payload}")
    
    async def send_message(self, message: Dict, timeout: float = 30.0) -> Dict:
        """Send message and wait for response"""
        if not self.is_connected():
            raise ConnectionError("Extension bridge not connected")
        
        message_id = message.get('id', str(uuid.uuid4()))
        message['id'] = message_id
        message['timestamp'] = time.time()
        
        # Create future for response
        future = asyncio.Future()
        self.pending_requests[message_id] = future
        
        try:
            # Send message
            await self._send_raw_message(message)
            
            # Wait for response with timeout
            response = await asyncio.wait_for(future, timeout=timeout)
            return response
            
        except asyncio.TimeoutError:
            self.pending_requests.pop(message_id, None)
            raise TimeoutError(f"Message timeout after {timeout} seconds")
        except Exception as e:
            self.pending_requests.pop(message_id, None)
            raise
    
    async def send_streaming_message(self, message: Dict) -> AsyncGenerator[Dict, None]:
        """Send message and yield streaming responses"""
        if not self.is_connected():
            raise ConnectionError("Extension bridge not connected")
        
        message_id = message.get('id', str(uuid.uuid4()))
        message['id'] = message_id
        message['timestamp'] = time.time()
        
        # Create queue for streaming responses
        queue = asyncio.Queue()
        self.streaming_requests[message_id] = queue
        
        try:
            # Send message
            await self._send_raw_message(message)
            
            # Yield streaming responses
            while True:
                try:
                    chunk = await asyncio.wait_for(queue.get(), timeout=60.0)
                    
                    if chunk is None:  # End of stream
                        break
                    elif isinstance(chunk, Exception):  # Error
                        raise chunk
                    else:
                        yield chunk
                        
                except asyncio.TimeoutError:
                    raise TimeoutError("Streaming timeout")
                    
        finally:
            self.streaming_requests.pop(message_id, None)
    
    async def _send_raw_message(self, message: Dict):
        """Send raw message via WebSocket"""
        if not self.websocket:
            raise ConnectionError("WebSocket not connected")
        
        try:
            message_str = json.dumps(message)
            await self.websocket.send(message_str)
            self.messages_sent += 1
            
            self.logger.debug(f"Sent message: {message.get('type')} (id: {message.get('id')})")
            
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            raise
    
    def is_connected(self) -> bool:
        """Check if bridge is connected"""
        return self.websocket is not None and not self.websocket.closed
    
    async def update_config(self, config_update: Dict):
        """Send configuration update to extension"""
        message = {
            "type": "config_update",
            "payload": config_update
        }
        
        try:
            await self.send_message(message, timeout=10.0)
            self.logger.info("Configuration updated successfully")
        except Exception as e:
            self.logger.error(f"Failed to update configuration: {e}")
            raise
    
    def get_stats(self) -> Dict:
        """Get bridge statistics"""
        uptime = 0
        if self.connection_start_time:
            uptime = time.time() - self.connection_start_time
        
        return {
            "connected": self.is_connected(),
            "uptime": uptime,
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "pending_requests": len(self.pending_requests),
            "streaming_requests": len(self.streaming_requests),
            "connection_attempts": self.connection_attempts
        }
