{"name": "es6-promisify", "version": "7.0.0", "description": "Converts callback-based functions to ES6 Promises", "main": "./dist/promisify.js", "module": "./dist/promisify.mjs", "exports": {"import": "./dist/promisify.mjs", "require": "./dist/promisify.js"}, "author": "<PERSON> <<EMAIL>>", "keywords": ["promise", "promises", "es6", "promisify", "es6-promisify"], "license": "MIT", "engines": {"node": ">=6"}, "scripts": {"prettier": "prettier --write lib", "lint": "eslint lib test", "pretest": "npm run build", "build:es": "babel lib/promisify.js -o dist/promisify.mjs --config-file ./babel.config-module.js", "build:cjs": "babel lib/promisify.js -o dist/promisify.js --config-file ./babel.config-nomodule.js", "build": "npm run build:es && npm run build:cjs", "test": "tape test", "test:coverage": "nyc npm test"}, "bugs": "http://github.com/mikehall314/es6-promisify/issues", "files": ["dist/promisify.js", "dist/promisify.mjs"], "repository": {"type": "git", "url": "mikehall314/es6-promisify"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "^7.14.3", "@babel/preset-env": "^7.14.2", "babel-preset-minify": "^0.5.1", "es6-promise": "^4.2.8", "eslint": "^7.27.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.23.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "husky": "^6.0.0", "nyc": "^15.1.0", "prettier": "^2.3.0", "sinon": "^10.0.0", "tape": "^5.2.2"}}