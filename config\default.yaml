# Universal Chatbot Proxy - Default Configuration

# Server Configuration
server:
  host: "127.0.0.1"
  port: 11435
  debug: false
  reload: false
  workers: 1

# WebSocket Configuration
websocket:
  port: 11436
  ping_interval: 30
  ping_timeout: 10

# Security Configuration
security:
  api_key_required: false
  api_keys: []
  cors_origins: ["*"]
  rate_limit_per_minute: 60

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: null
  rotation: "1 day"
  retention: "30 days"

# Performance Configuration
performance:
  max_concurrent_requests: 10
  connection_pool_size: 50
  keepalive_timeout: 65
  request_timeout: 30

# Monitoring Configuration
monitoring:
  metrics_enabled: false
  prometheus_port: 9090
  health_check_interval: 30

# Extension Settings
extension_id: null
development_mode: false

# Default Settings
default_chatbot: "chatgpt"
default_model: "gpt-3.5-turbo"

# Chatbot Configurations
chatbots:
  chatgpt:
    url: "https://chat.openai.com"
    enabled: true
    models: ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
    timeout: 30
    max_retries: 3
    selectors:
      input: 'textarea[data-id="root"]'
      submit: 'button[data-testid="send-button"]'
      output: '[data-message-author-role="assistant"]'
      loading: '.result-streaming'
      error: '.text-red-500'

  kimi:
    url: "https://kimi.moonshot.cn"
    enabled: true
    models: ["kimi", "moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"]
    timeout: 30
    max_retries: 3
    selectors:
      input: '.chat-input textarea'
      submit: '.send-button'
      output: '.message-content'
      loading: '.typing-indicator'
      error: '.error-message'

  claude:
    url: "https://claude.ai"
    enabled: false
    models: ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"]
    timeout: 30
    max_retries: 3
    selectors:
      input: 'div[contenteditable="true"]'
      submit: 'button[aria-label="Send Message"]'
      output: '.font-claude-message'
      loading: '.animate-pulse'
      error: '.text-red-600'

  custom:
    url: "https://example.com/chat"
    enabled: false
    models: ["custom-model"]
    timeout: 30
    max_retries: 3
    selectors:
      input: '#chat-input'
      submit: '#send-button'
      output: '.response-container'
      loading: '.loading-indicator'
      error: '.error-container'
