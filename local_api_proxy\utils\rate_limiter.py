"""
Universal Chatbot Proxy - Rate Limiter
Token bucket rate limiting implementation
"""

import asyncio
import time
from typing import Dict, Optional
from dataclasses import dataclass


@dataclass
class TokenBucket:
    """Token bucket for rate limiting"""
    capacity: int
    tokens: float
    refill_rate: float
    last_refill: float
    
    def refill(self) -> None:
        """Refill tokens based on elapsed time"""
        now = time.time()
        elapsed = now - self.last_refill
        self.tokens = min(self.capacity, self.tokens + elapsed * self.refill_rate)
        self.last_refill = now
    
    def consume(self, tokens: int = 1) -> bool:
        """Try to consume tokens"""
        self.refill()
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        return False


class RateLimiter:
    """Rate limiter using token bucket algorithm"""
    
    def __init__(self, requests_per_minute: int = 60, burst_capacity: Optional[int] = None):
        self.requests_per_minute = requests_per_minute
        self.burst_capacity = burst_capacity or min(requests_per_minute, 10)
        self.refill_rate = requests_per_minute / 60.0  # tokens per second
        self.buckets: Dict[str, TokenBucket] = {}
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    async def is_allowed(self, identifier: str, tokens: int = 1) -> bool:
        """Check if request is allowed for identifier"""
        await self._cleanup_old_buckets()
        
        bucket = self._get_bucket(identifier)
        return bucket.consume(tokens)
    
    def _get_bucket(self, identifier: str) -> TokenBucket:
        """Get or create token bucket for identifier"""
        if identifier not in self.buckets:
            now = time.time()
            self.buckets[identifier] = TokenBucket(
                capacity=self.burst_capacity,
                tokens=self.burst_capacity,
                refill_rate=self.refill_rate,
                last_refill=now
            )
        return self.buckets[identifier]
    
    async def _cleanup_old_buckets(self) -> None:
        """Remove old unused buckets"""
        now = time.time()
        if now - self.last_cleanup < self.cleanup_interval:
            return
        
        # Remove buckets that haven't been used in the last hour
        cutoff = now - 3600
        to_remove = [
            identifier for identifier, bucket in self.buckets.items()
            if bucket.last_refill < cutoff
        ]
        
        for identifier in to_remove:
            del self.buckets[identifier]
        
        self.last_cleanup = now
    
    def get_remaining_tokens(self, identifier: str) -> int:
        """Get remaining tokens for identifier"""
        if identifier not in self.buckets:
            return self.burst_capacity
        
        bucket = self.buckets[identifier]
        bucket.refill()
        return int(bucket.tokens)
    
    def get_reset_time(self, identifier: str) -> float:
        """Get time when bucket will be full again"""
        if identifier not in self.buckets:
            return time.time()
        
        bucket = self.buckets[identifier]
        bucket.refill()
        
        if bucket.tokens >= bucket.capacity:
            return time.time()
        
        tokens_needed = bucket.capacity - bucket.tokens
        seconds_to_full = tokens_needed / bucket.refill_rate
        return time.time() + seconds_to_full


class AdaptiveRateLimiter(RateLimiter):
    """Rate limiter that adapts based on error rates"""
    
    def __init__(self, requests_per_minute: int = 60, burst_capacity: Optional[int] = None):
        super().__init__(requests_per_minute, burst_capacity)
        self.error_counts: Dict[str, int] = {}
        self.success_counts: Dict[str, int] = {}
        self.adaptation_window = 300  # 5 minutes
        self.last_adaptation = time.time()
    
    async def record_success(self, identifier: str) -> None:
        """Record successful request"""
        self.success_counts[identifier] = self.success_counts.get(identifier, 0) + 1
        await self._adapt_limits()
    
    async def record_error(self, identifier: str) -> None:
        """Record failed request"""
        self.error_counts[identifier] = self.error_counts.get(identifier, 0) + 1
        await self._adapt_limits()
    
    async def _adapt_limits(self) -> None:
        """Adapt rate limits based on error rates"""
        now = time.time()
        if now - self.last_adaptation < self.adaptation_window:
            return
        
        for identifier in set(self.error_counts.keys()) | set(self.success_counts.keys()):
            errors = self.error_counts.get(identifier, 0)
            successes = self.success_counts.get(identifier, 0)
            total = errors + successes
            
            if total < 10:  # Not enough data
                continue
            
            error_rate = errors / total
            
            if identifier in self.buckets:
                bucket = self.buckets[identifier]
                
                if error_rate > 0.5:  # High error rate, reduce capacity
                    bucket.capacity = max(1, int(bucket.capacity * 0.8))
                    bucket.refill_rate = max(0.1, bucket.refill_rate * 0.8)
                elif error_rate < 0.1:  # Low error rate, increase capacity
                    bucket.capacity = min(self.burst_capacity, int(bucket.capacity * 1.2))
                    bucket.refill_rate = min(self.refill_rate, bucket.refill_rate * 1.2)
        
        # Reset counters
        self.error_counts.clear()
        self.success_counts.clear()
        self.last_adaptation = now
