# 🏗️ Universal Chatbot Proxy - Architecture

## Overview

The Universal Chatbot Proxy is a sophisticated system that bridges the gap between web-based chatbots and standardized API interfaces. It consists of three main components working in harmony to provide seamless OpenAI API compatibility.

## System Components

### 1. Chrome Extension
**Purpose**: Direct interaction with chatbot web interfaces
**Technology**: JavaScript (Manifest v3)
**Location**: Browser environment

### 2. Local API Proxy
**Purpose**: OpenAI-compatible API server
**Technology**: Python FastAPI
**Location**: Local machine (localhost:11435)

### 3. Communication Bridge
**Purpose**: Real-time communication between components
**Technology**: WebSocket + Native Messaging
**Location**: Cross-process communication

## Data Flow Architecture

```mermaid
graph TD
    A[Client Application] -->|OpenAI API Call| B[Local API Proxy]
    B -->|WebSocket Message| C[Chrome Extension Background]
    C -->|Content Script Message| D[Chatbot Web Page]
    D -->|DOM Response| C
    C -->|Parsed Response| B
    B -->|OpenAI Format| A
    
    subgraph "Browser Environment"
        C
        D
        E[Content Script]
        F[Extension UI]
        C --> E
        C --> F
    end
    
    subgraph "Local Machine"
        B
        G[OpenAI Compatibility Layer]
        H[Bridge Client]
        B --> G
        B --> H
    end
```

## Component Details

### Chrome Extension Architecture

#### Manifest v3 Structure
```json
{
  "manifest_version": 3,
  "service_worker": "background.js",
  "content_scripts": [{
    "matches": ["https://chat.openai.com/*", "https://kimi.moonshot.cn/*"],
    "js": ["contentScript.js"]
  }],
  "permissions": ["activeTab", "storage", "nativeMessaging"]
}
```

#### Background Script (Service Worker)
- **Message Routing**: Coordinates between content scripts and local API
- **Session Management**: Maintains chatbot session state
- **Connection Handling**: Manages WebSocket connections
- **Error Recovery**: Implements retry logic and fallback mechanisms

#### Content Script
- **DOM Manipulation**: Injects messages into chatbot interfaces
- **Response Extraction**: Monitors and extracts chatbot responses
- **Human Simulation**: Implements human-like interaction patterns
- **Streaming Support**: Handles real-time response streaming

#### Extension UI
- **Popup Interface**: Quick configuration and status
- **Options Page**: Detailed settings and chatbot management
- **Status Indicators**: Real-time connection and operation status

### Local API Proxy Architecture

#### FastAPI Server Structure
```python
app = FastAPI(title="Universal Chatbot Proxy")

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    # OpenAI-compatible endpoint implementation
    pass

@app.post("/v1/completions")
async def completions(request: CompletionRequest):
    # Legacy completion endpoint
    pass
```

#### Core Modules

**server.py**
- Main FastAPI application
- Route definitions
- Middleware configuration
- Error handling

**openai_compat.py**
- OpenAI API response formatting
- Model mapping and translation
- Streaming response handling
- Token counting simulation

**bridge_client.py**
- WebSocket client implementation
- Message serialization/deserialization
- Connection management
- Retry logic

**models.py**
- Pydantic data models
- Request/response schemas
- Validation logic

### Communication Protocol

#### WebSocket Message Format
```json
{
  "id": "unique-request-id",
  "type": "chat_completion",
  "payload": {
    "messages": [...],
    "model": "gpt-3.5-turbo",
    "stream": true
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Response Format
```json
{
  "id": "unique-request-id",
  "type": "response",
  "status": "success|error|streaming",
  "payload": {
    "content": "Response text",
    "done": false
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Chatbot Adapter System

### Adapter Interface
```javascript
class ChatbotAdapter {
  constructor(config) {
    this.selectors = config.selectors;
    this.delays = config.delays;
  }
  
  async sendMessage(message) { /* Implementation */ }
  async waitForResponse() { /* Implementation */ }
  async extractResponse() { /* Implementation */ }
}
```

### Supported Chatbots

#### ChatGPT Adapter
- **Input Selector**: `textarea[data-id="root"]`
- **Submit Method**: Click send button or Enter key
- **Response Container**: `div[data-message-author-role="assistant"]`
- **Streaming Detection**: MutationObserver on response container

#### Kimi Adapter
- **Input Selector**: Custom Kimi-specific selectors
- **Submit Method**: Kimi's submission mechanism
- **Response Container**: Kimi's response structure
- **Streaming Detection**: Kimi-specific streaming patterns

#### Generic Adapter
- **Configurable Selectors**: User-defined DOM selectors
- **Flexible Input Methods**: Support for various input types
- **Custom Response Extraction**: Configurable response parsing
- **Adaptive Streaming**: Generic streaming detection

## Security Architecture

### Local-Only Processing
- No external API calls to LLM providers
- All data processing happens locally
- No user data transmitted to external servers

### Browser Security
- Content Security Policy compliance
- Secure message passing between contexts
- Permission-based access control

### Communication Security
- Local WebSocket connections only
- Message validation and sanitization
- Rate limiting and abuse prevention

## Performance Considerations

### Optimization Strategies
- **Connection Pooling**: Reuse WebSocket connections
- **Response Caching**: Cache recent responses for duplicate requests
- **DOM Optimization**: Efficient DOM observation and manipulation
- **Memory Management**: Prevent memory leaks in long-running sessions

### Latency Factors
- **Network Latency**: Local communication (minimal)
- **DOM Processing**: Browser rendering and manipulation
- **Human Simulation**: Intentional delays for detection resistance
- **Chatbot Response Time**: Dependent on target chatbot performance

## Error Handling Strategy

### Error Categories
1. **Network Errors**: Connection failures, timeouts
2. **DOM Errors**: Element not found, structure changes
3. **Chatbot Errors**: Rate limiting, captchas, service unavailable
4. **Protocol Errors**: Message format issues, version mismatches

### Recovery Mechanisms
- **Exponential Backoff**: Progressive retry delays
- **Fallback Selectors**: Alternative DOM selectors
- **Session Recovery**: Restore interrupted sessions
- **Graceful Degradation**: Partial functionality when components fail

## Scalability Considerations

### Horizontal Scaling
- Multiple chatbot instances
- Load balancing across browser tabs
- Parallel request processing

### Vertical Scaling
- Resource optimization
- Memory usage monitoring
- CPU usage optimization

## Future Architecture Enhancements

### Planned Improvements
- **Plugin System**: Third-party chatbot adapters
- **Cloud Sync**: Optional configuration synchronization
- **Advanced Analytics**: Usage metrics and performance monitoring
- **Multi-Browser Support**: Firefox, Safari, Edge compatibility

### Extension Points
- **Custom Adapters**: Developer API for new chatbots
- **Middleware System**: Request/response transformation
- **Event System**: Hooks for custom functionality
- **Configuration API**: Programmatic configuration management
