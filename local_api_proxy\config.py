"""Configuration management for Universal Chatbot Proxy."""

import os
import yaml
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings
from pathlib import Path


class ChatbotConfig(BaseSettings):
    """Configuration for a specific chatbot."""
    
    url: str
    enabled: bool = True
    models: List[str] = []
    timeout: int = 30
    max_retries: int = 3
    selectors: Optional[Dict[str, str]] = None
    
    class Config:
        env_prefix = "CHATBOT_"


class ServerConfig(BaseSettings):
    """Server configuration."""
    
    host: str = Field(default="127.0.0.1", env="API_HOST")
    port: int = Field(default=11435, env="API_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    reload: bool = Field(default=False, env="RELOAD_ON_CHANGE")
    workers: int = Field(default=1, env="WORKERS")
    
    class Config:
        env_prefix = "SERVER_"


class WebSocketConfig(BaseSettings):
    """WebSocket configuration."""
    
    port: int = Field(default=11436, env="WEBSOCKET_PORT")
    ping_interval: int = Field(default=30, env="WS_PING_INTERVAL")
    ping_timeout: int = Field(default=10, env="WS_PING_TIMEOUT")
    
    class Config:
        env_prefix = "WS_"


class SecurityConfig(BaseSettings):
    """Security configuration."""
    
    api_key_required: bool = Field(default=False, env="API_KEY_REQUIRED")
    api_keys: List[str] = Field(default_factory=list, env="API_KEYS")
    cors_origins: List[str] = Field(default_factory=lambda: ["*"], env="CORS_ORIGINS")
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    
    class Config:
        env_prefix = "SECURITY_"


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    file_path: Optional[str] = Field(default=None, env="LOG_FILE_PATH")
    rotation: str = Field(default="1 day", env="LOG_ROTATION")
    retention: str = Field(default="30 days", env="LOG_RETENTION")
    
    class Config:
        env_prefix = "LOG_"


class PerformanceConfig(BaseSettings):
    """Performance configuration."""
    
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    connection_pool_size: int = Field(default=50, env="CONNECTION_POOL_SIZE")
    keepalive_timeout: int = Field(default=65, env="KEEPALIVE_TIMEOUT")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    
    class Config:
        env_prefix = "PERF_"


class MonitoringConfig(BaseSettings):
    """Monitoring configuration."""
    
    metrics_enabled: bool = Field(default=False, env="METRICS_ENABLED")
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    class Config:
        env_prefix = "MONITORING_"


class Config(BaseSettings):
    """Main configuration class."""
    
    # Core configurations
    server: ServerConfig = Field(default_factory=ServerConfig)
    websocket: WebSocketConfig = Field(default_factory=WebSocketConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    
    # Chatbot configurations
    chatbots: Dict[str, ChatbotConfig] = Field(default_factory=dict)
    
    # Extension settings
    extension_id: Optional[str] = Field(default=None, env="EXTENSION_ID")
    development_mode: bool = Field(default=False, env="DEVELOPMENT_MODE")
    
    # Default settings
    default_chatbot: str = Field(default="chatgpt", env="DEFAULT_CHATBOT")
    default_model: str = Field(default="gpt-3.5-turbo", env="DEFAULT_MODEL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @classmethod
    def load_from_file(cls, config_path: str) -> "Config":
        """Load configuration from YAML file."""
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_file, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)
    
    def save_to_file(self, config_path: str) -> None:
        """Save configuration to YAML file."""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, "w", encoding="utf-8") as f:
            yaml.dump(self.dict(), f, default_flow_style=False, indent=2)
    
    def get_chatbot_config(self, chatbot_name: str) -> Optional[ChatbotConfig]:
        """Get configuration for a specific chatbot."""
        return self.chatbots.get(chatbot_name)
    
    def add_chatbot(self, name: str, config: ChatbotConfig) -> None:
        """Add a new chatbot configuration."""
        self.chatbots[name] = config
    
    def remove_chatbot(self, name: str) -> bool:
        """Remove a chatbot configuration."""
        if name in self.chatbots:
            del self.chatbots[name]
            return True
        return False


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = load_config()
    return _config


def load_config(config_path: Optional[str] = None) -> Config:
    """Load configuration from file or environment."""
    global _config
    
    if config_path:
        _config = Config.load_from_file(config_path)
    else:
        # Try to load from default locations
        default_paths = [
            "config/production.yaml",
            "config/development.yaml",
            "config/default.yaml",
        ]
        
        for path in default_paths:
            if Path(path).exists():
                _config = Config.load_from_file(path)
                break
        else:
            # Load from environment variables only
            _config = Config()
    
    # Add default chatbot configurations if none exist
    if not _config.chatbots:
        _config.chatbots = {
            "chatgpt": ChatbotConfig(
                url="https://chat.openai.com",
                models=["gpt-3.5-turbo", "gpt-4"],
                selectors={
                    "input": 'textarea[data-id="root"]',
                    "submit": 'button[data-testid="send-button"]',
                    "output": '[data-message-author-role="assistant"]',
                }
            ),
            "kimi": ChatbotConfig(
                url="https://kimi.moonshot.cn",
                models=["kimi"],
                selectors={
                    "input": ".chat-input textarea",
                    "submit": ".send-button",
                    "output": ".message-content",
                }
            ),
        }
    
    return _config


def reload_config(config_path: Optional[str] = None) -> Config:
    """Reload configuration."""
    global _config
    _config = None
    return load_config(config_path)
