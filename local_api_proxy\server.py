#!/usr/bin/env python3
"""
Universal Chatbot Proxy - Main FastAPI Server
Provides OpenAI-compatible API endpoints for chatbot interaction
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

from .config import get_config, Config
from .models import (
    ChatCompletionRequest, ChatCompletionResponse,
    CompletionRequest, CompletionResponse,
    ModelsResponse, HealthResponse, ErrorResponse
)
from .openai_compat import OpenAICompatibilityLayer
from .bridge_client import ExtensionBridge
from .utils.logging import setup_logging
from .utils.rate_limiter import RateLimiter
from .utils.metrics import MetricsCollector

# Global instances
extension_bridge: Optional[ExtensionBridge] = None
openai_compat: Optional[OpenAICompatibilityLayer] = None
rate_limiter: Optional[RateLimiter] = None
metrics: Optional[MetricsCollector] = None

# Security
security = HTTPBearer(auto_error=False)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global extension_bridge, openai_compat, rate_limiter, metrics
    
    config = get_config()
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize components
        logger.info("Starting Universal Chatbot Proxy server...")
        
        # Setup metrics collection
        if config.monitoring.metrics_enabled:
            metrics = MetricsCollector()
            await metrics.start()
        
        # Setup rate limiter
        rate_limiter = RateLimiter(
            requests_per_minute=config.security.rate_limit_per_minute
        )
        
        # Initialize OpenAI compatibility layer
        openai_compat = OpenAICompatibilityLayer(config)
        
        # Initialize extension bridge
        extension_bridge = ExtensionBridge(config)
        await extension_bridge.start()
        
        logger.info("Server started successfully")
        yield
        
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down server...")
        
        if extension_bridge:
            await extension_bridge.stop()
        
        if metrics:
            await metrics.stop()
        
        logger.info("Server shutdown complete")

# Create FastAPI app
def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    config = get_config()
    
    app = FastAPI(
        title="Universal Chatbot Proxy",
        description="OpenAI-compatible API for web-based chatbots",
        version="1.0.0",
        docs_url="/docs" if config.server.debug else None,
        redoc_url="/redoc" if config.server.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.security.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )
    
    return app

app = create_app()

# Dependency functions
async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)) -> bool:
    """Verify API key if required"""
    config = get_config()
    
    if not config.security.api_key_required:
        return True
    
    if not credentials:
        raise HTTPException(status_code=401, detail="API key required")
    
    if credentials.credentials not in config.security.api_keys:
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return True

async def check_rate_limit(request: Request) -> bool:
    """Check rate limiting"""
    if not rate_limiter:
        return True
    
    client_ip = request.client.host
    if not await rate_limiter.is_allowed(client_ip):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    return True

# API Routes

@app.get("/v1/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    config = get_config()
    
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        uptime=0,  # TODO: Calculate actual uptime
        extension_connected=extension_bridge.is_connected() if extension_bridge else False,
        active_chatbots=list(config.chatbots.keys()) if config.chatbots else [],
        timestamp=None
    )

@app.get("/v1/models", response_model=ModelsResponse)
async def list_models(
    _: bool = Depends(verify_api_key),
    __: bool = Depends(check_rate_limit)
):
    """List available models (chatbot configurations)"""
    if not openai_compat:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return await openai_compat.list_models()

@app.post("/v1/chat/completions")
async def create_chat_completion(
    request: ChatCompletionRequest,
    _: bool = Depends(verify_api_key),
    __: bool = Depends(check_rate_limit)
):
    """Create a chat completion"""
    if not openai_compat or not extension_bridge:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    try:
        # Record metrics
        if metrics:
            metrics.record_request("chat_completion", request.model)
        
        # Process request
        if request.stream:
            return StreamingResponse(
                openai_compat.create_chat_completion_stream(request, extension_bridge),
                media_type="text/plain"
            )
        else:
            response = await openai_compat.create_chat_completion(request, extension_bridge)
            return response
            
    except Exception as e:
        logging.error(f"Chat completion error: {e}")
        
        if metrics:
            metrics.record_error("chat_completion", str(e))
        
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/v1/completions")
async def create_completion(
    request: CompletionRequest,
    _: bool = Depends(verify_api_key),
    __: bool = Depends(check_rate_limit)
):
    """Create a text completion (legacy endpoint)"""
    if not openai_compat or not extension_bridge:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    try:
        # Record metrics
        if metrics:
            metrics.record_request("completion", request.model)
        
        # Convert to chat completion format
        chat_request = ChatCompletionRequest(
            model=request.model,
            messages=[{"role": "user", "content": request.prompt}],
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            frequency_penalty=request.frequency_penalty,
            presence_penalty=request.presence_penalty,
            stream=request.stream
        )
        
        if request.stream:
            return StreamingResponse(
                openai_compat.create_completion_stream(chat_request, extension_bridge),
                media_type="text/plain"
            )
        else:
            response = await openai_compat.create_completion(chat_request, extension_bridge)
            return response
            
    except Exception as e:
        logging.error(f"Completion error: {e}")
        
        if metrics:
            metrics.record_error("completion", str(e))
        
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/v1/config")
async def get_configuration(
    _: bool = Depends(verify_api_key)
):
    """Get current configuration"""
    config = get_config()
    
    return {
        "chatbots": {
            name: {
                "url": chatbot.url,
                "models": chatbot.models,
                "enabled": chatbot.enabled
            }
            for name, chatbot in config.chatbots.items()
        },
        "default_chatbot": config.default_chatbot,
        "default_model": config.default_model
    }

@app.put("/v1/config")
async def update_configuration(
    config_update: dict,
    _: bool = Depends(verify_api_key)
):
    """Update configuration"""
    try:
        config = get_config()
        
        # Update configuration
        # TODO: Implement configuration update logic
        
        # Notify extension bridge of config changes
        if extension_bridge:
            await extension_bridge.update_config(config_update)
        
        return {"success": True, "message": "Configuration updated"}
        
    except Exception as e:
        logging.error(f"Configuration update error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/metrics")
async def get_metrics():
    """Get Prometheus metrics (if enabled)"""
    config = get_config()
    
    if not config.monitoring.metrics_enabled or not metrics:
        raise HTTPException(status_code=404, detail="Metrics not enabled")
    
    return metrics.get_prometheus_metrics()

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error={
                "message": exc.detail,
                "type": "http_error",
                "code": exc.status_code
            }
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logging.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error={
                "message": "Internal server error",
                "type": "internal_error",
                "code": 500
            }
        ).dict()
    )

# Main function
def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Universal Chatbot Proxy Server")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--host", default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=11435, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        from .config import load_config
        config = load_config(args.config)
    else:
        config = get_config()
    
    # Setup logging
    setup_logging(config.logging)
    logger = logging.getLogger(__name__)
    
    # Override config with command line arguments
    if args.debug:
        config.server.debug = True
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start server
    logger.info(f"Starting server on {args.host}:{args.port}")
    
    uvicorn.run(
        "local_api_proxy.server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="debug" if config.server.debug else "info",
        access_log=config.server.debug
    )

if __name__ == "__main__":
    main()
