/* Universal Chatbot Proxy - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    width: 380px;
    min-height: 500px;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 600px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    border-radius: 0 0 12px 12px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.logo-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    opacity: 0.9;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff4757;
    transition: background-color 0.3s ease;
}

.status-dot.connected {
    background: #2ed573;
}

.status-dot.connecting {
    background: #ffa502;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.section {
    margin-bottom: 20px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Connection Info */
.connection-info {
    background: white;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e1e8ed;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
}

.info-row:not(:last-child) {
    border-bottom: 1px solid #f1f3f4;
    margin-bottom: 4px;
    padding-bottom: 8px;
}

.label {
    font-weight: 500;
    color: #5f6368;
}

.value {
    font-weight: 600;
    color: #1a73e8;
}

.value.error {
    color: #d93025;
}

.value.success {
    color: #137333;
}

/* Actions */
.actions {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: #1a73e8;
    color: white;
}

.btn-primary:hover {
    background: #1557b0;
}

.btn-secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.btn-secondary:hover {
    background: #f1f3f4;
}

.btn-small {
    padding: 6px 10px;
    font-size: 11px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-icon {
    font-size: 14px;
}

/* Chatbot Selector */
.chatbot-selector {
    display: flex;
    gap: 8px;
    align-items: center;
}

.select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 6px;
    font-size: 12px;
    background: white;
    color: #3c4043;
}

.select:focus {
    outline: none;
    border-color: #1a73e8;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* Activity Log */
.activity-log {
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
    max-height: 120px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    font-size: 12px;
    border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: #5f6368;
    font-weight: 500;
    min-width: 40px;
}

.activity-text {
    color: #3c4043;
}

/* Error Section */
.error-section {
    background: #fef7f0;
    border: 1px solid #f9ab55;
    border-radius: 8px;
    padding: 12px;
}

.error-title {
    color: #b06000;
    margin-bottom: 8px;
}

.error-message {
    background: white;
    border: 1px solid #f9ab55;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
    color: #b06000;
    margin-bottom: 8px;
    word-break: break-word;
}

/* Footer */
.footer {
    background: white;
    border-top: 1px solid #e1e8ed;
    padding: 12px 16px;
    border-radius: 12px 12px 0 0;
}

.footer-links {
    display: flex;
    justify-content: space-around;
    margin-bottom: 8px;
}

.link-btn {
    background: none;
    border: none;
    color: #1a73e8;
    font-size: 12px;
    cursor: pointer;
    text-decoration: underline;
    padding: 4px 8px;
}

.link-btn:hover {
    color: #1557b0;
}

.version {
    text-align: center;
    font-size: 10px;
    color: #9aa0a6;
}

/* Scrollbar */
.activity-log::-webkit-scrollbar,
.main::-webkit-scrollbar {
    width: 4px;
}

.activity-log::-webkit-scrollbar-track,
.main::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.activity-log::-webkit-scrollbar-thumb,
.main::-webkit-scrollbar-thumb {
    background: #dadce0;
    border-radius: 2px;
}

.activity-log::-webkit-scrollbar-thumb:hover,
.main::-webkit-scrollbar-thumb:hover {
    background: #bdc1c6;
}

/* Responsive adjustments */
@media (max-height: 500px) {
    .main {
        max-height: 300px;
    }
    
    .activity-log {
        max-height: 80px;
    }
}
