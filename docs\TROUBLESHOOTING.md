# 🔧 Troubleshooting Guide

## Quick Diagnostics

### Health Check Commands

```bash
# Check API server status
curl http://localhost:11435/v1/health

# Test basic API functionality
curl -X POST http://localhost:11435/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"test"}]}'

# Check WebSocket connection
wscat -c ws://localhost:11436

# Verify extension installation
chrome://extensions/ (check if extension is loaded and enabled)
```

## Common Issues

### 1. API Server Issues

#### Server Won't Start

**Symptoms:**
- `Connection refused` errors
- Server process exits immediately
- Port binding errors

**Solutions:**

```bash
# Check if port is already in use
netstat -tulpn | grep 11435
lsof -i :11435  # macOS/Linux

# Kill existing process
kill -9 $(lsof -t -i:11435)

# Try different port
python local_api_proxy/server.py --port 11436

# Check Python dependencies
pip install -r requirements.txt

# Verify Python version
python --version  # Should be 3.9+
```

#### Server Crashes

**Symptoms:**
- Server stops responding
- Error messages in logs
- High memory/CPU usage

**Solutions:**

```bash
# Check logs for errors
tail -f logs/server.log

# Monitor resource usage
htop
ps aux | grep python

# Restart with debug mode
python local_api_proxy/server.py --debug

# Check for memory leaks
python -m memory_profiler local_api_proxy/server.py
```

### 2. Chrome Extension Issues

#### Extension Not Loading

**Symptoms:**
- Extension not visible in Chrome
- "Failed to load extension" error
- Manifest errors

**Solutions:**

1. **Check Manifest Syntax**
   ```bash
   # Validate JSON syntax
   python -m json.tool extension/manifest.json
   ```

2. **Verify Permissions**
   ```json
   // Ensure required permissions in manifest.json
   {
     "permissions": [
       "activeTab",
       "storage", 
       "nativeMessaging"
     ]
   }
   ```

3. **Clear Extension Data**
   - Go to `chrome://extensions/`
   - Remove and reload the extension
   - Clear browser cache and cookies

#### Extension Connection Failed

**Symptoms:**
- "Connection to API server failed"
- WebSocket connection errors
- No response from chatbots

**Solutions:**

```javascript
// Check extension console for errors
// Right-click extension icon → Inspect popup

// Test WebSocket connection manually
const ws = new WebSocket('ws://localhost:11436');
ws.onopen = () => console.log('Connected');
ws.onerror = (error) => console.error('WebSocket error:', error);

// Verify API server is running
fetch('http://localhost:11435/v1/health')
  .then(response => response.json())
  .then(data => console.log('Server status:', data));
```

### 3. Chatbot Integration Issues

#### ChatGPT Not Responding

**Symptoms:**
- Messages sent but no response
- "Element not found" errors
- Infinite loading

**Solutions:**

1. **Update Selectors**
   ```javascript
   // Check current ChatGPT selectors
   const inputSelector = 'textarea[data-id="root"]';
   const submitSelector = 'button[data-testid="send-button"]';
   const outputSelector = '[data-message-author-role="assistant"]';
   
   // Test selectors in browser console
   document.querySelector(inputSelector);
   ```

2. **Handle UI Changes**
   ```javascript
   // Implement fallback selectors
   const inputSelectors = [
     'textarea[data-id="root"]',
     '#prompt-textarea',
     'textarea[placeholder*="message"]'
   ];
   
   function findInput() {
     for (const selector of inputSelectors) {
       const element = document.querySelector(selector);
       if (element) return element;
     }
     return null;
   }
   ```

3. **Check for Rate Limiting**
   - Look for CAPTCHA challenges
   - Check for "Too many requests" messages
   - Wait and retry after delays

#### Kimi Integration Issues

**Symptoms:**
- Kimi-specific connection failures
- Different response format
- Selector mismatches

**Solutions:**

```javascript
// Update Kimi selectors (these may change)
const kimiSelectors = {
  input: '.chat-input textarea',
  submit: '.send-button',
  output: '.message-content',
  loading: '.typing-indicator'
};

// Handle Kimi's specific response format
function extractKimiResponse(element) {
  // Kimi may use different DOM structure
  const content = element.querySelector('.message-text');
  return content ? content.textContent : '';
}
```

### 4. Communication Issues

#### WebSocket Connection Problems

**Symptoms:**
- Connection timeouts
- Frequent disconnections
- Message delivery failures

**Solutions:**

```python
# Server-side WebSocket debugging
import logging
logging.getLogger('websockets').setLevel(logging.DEBUG)

# Implement reconnection logic
async def connect_with_retry(uri, max_retries=5):
    for attempt in range(max_retries):
        try:
            return await websockets.connect(uri)
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)  # Exponential backoff
```

```javascript
// Client-side reconnection
class WebSocketManager {
  constructor(url) {
    this.url = url;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.connect();
  }
  
  connect() {
    this.ws = new WebSocket(this.url);
    
    this.ws.onclose = () => {
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        setTimeout(() => {
          this.reconnectAttempts++;
          this.connect();
        }, 1000 * Math.pow(2, this.reconnectAttempts));
      }
    };
  }
}
```

### 5. Performance Issues

#### Slow Response Times

**Symptoms:**
- Long delays between request and response
- Timeouts
- High CPU/memory usage

**Solutions:**

1. **Optimize Server Configuration**
   ```python
   # Increase worker processes
   uvicorn.run(app, host="0.0.0.0", port=11435, workers=4)
   
   # Implement connection pooling
   import aiohttp
   connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
   ```

2. **Optimize Extension Performance**
   ```javascript
   // Debounce frequent operations
   function debounce(func, wait) {
     let timeout;
     return function executedFunction(...args) {
       const later = () => {
         clearTimeout(timeout);
         func(...args);
       };
       clearTimeout(timeout);
       timeout = setTimeout(later, wait);
     };
   }
   
   // Use efficient DOM queries
   const observer = new MutationObserver(debounce(handleChanges, 100));
   ```

#### Memory Leaks

**Symptoms:**
- Increasing memory usage over time
- Browser becomes unresponsive
- System slowdown

**Solutions:**

```javascript
// Proper cleanup in extension
class ContentScript {
  constructor() {
    this.observers = [];
    this.intervals = [];
  }
  
  cleanup() {
    // Disconnect observers
    this.observers.forEach(observer => observer.disconnect());
    
    // Clear intervals
    this.intervals.forEach(interval => clearInterval(interval));
    
    // Remove event listeners
    document.removeEventListener('click', this.handleClick);
  }
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  contentScript.cleanup();
});
```

## Error Codes Reference

### API Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 1001 | Extension not connected | Reload extension, check WebSocket |
| 1002 | Chatbot not responding | Check chatbot selectors, verify page |
| 1003 | Rate limit exceeded | Wait and retry, implement backoff |
| 1004 | Invalid request format | Check API request structure |
| 1005 | Timeout error | Increase timeout, check network |

### Extension Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 2001 | DOM element not found | Update selectors, check page structure |
| 2002 | Permission denied | Check extension permissions |
| 2003 | Script injection failed | Verify content script configuration |
| 2004 | Storage access failed | Check storage permissions |

## Debugging Tools

### Enable Debug Mode

```bash
# Server debug mode
python local_api_proxy/server.py --debug --log-level DEBUG

# Extension debug mode
# Add to manifest.json:
{
  "content_security_policy": {
    "extension_pages": "script-src 'self' 'unsafe-eval'; object-src 'self'"
  }
}
```

### Logging Configuration

```python
# Enhanced logging
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)
```

### Browser DevTools

1. **Extension Debugging**
   - Background script: `chrome://extensions/` → Inspect views
   - Content script: F12 on target page → Sources
   - Popup: Right-click extension icon → Inspect popup

2. **Network Monitoring**
   - F12 → Network tab
   - Filter by WebSocket connections
   - Monitor API requests

## Getting Help

### Before Reporting Issues

1. **Collect Information**
   ```bash
   # System information
   python --version
   chrome --version
   uname -a  # Linux/macOS
   systeminfo  # Windows
   
   # Error logs
   tail -n 50 logs/server.log
   ```

2. **Test Basic Functionality**
   ```bash
   # Test API server
   curl http://localhost:11435/v1/health
   
   # Test extension
   # Check chrome://extensions/ for errors
   ```

3. **Create Minimal Reproduction**
   - Isolate the issue
   - Provide step-by-step reproduction
   - Include relevant code snippets

### Support Channels

- **GitHub Issues**: Bug reports and feature requests
- **Discussions**: Questions and community support
- **Discord**: Real-time chat support
- **Email**: <EMAIL>

### Issue Template

```markdown
**Environment:**
- OS: [Windows/macOS/Linux]
- Python version: [3.9/3.10/3.11]
- Chrome version: [Version number]
- Extension version: [Version number]

**Issue Description:**
[Clear description of the problem]

**Steps to Reproduce:**
1. [First step]
2. [Second step]
3. [Third step]

**Expected Behavior:**
[What should happen]

**Actual Behavior:**
[What actually happens]

**Logs:**
```
[Paste relevant logs here]
```

**Additional Context:**
[Any other relevant information]
```
