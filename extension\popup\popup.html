<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Chatbot Proxy</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <img src="../icons/icon32.png" alt="Logo" class="logo-icon">
                <h1 class="title">Chatbot Proxy</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot" id="statusDot"></span>
                <span class="status-text" id="statusText">Disconnected</span>
            </div>
        </header>

        <main class="main">
            <!-- Connection Status -->
            <section class="section">
                <h2 class="section-title">Connection Status</h2>
                <div class="connection-info">
                    <div class="info-row">
                        <span class="label">API Server:</span>
                        <span class="value" id="apiStatus">Checking...</span>
                    </div>
                    <div class="info-row">
                        <span class="label">WebSocket:</span>
                        <span class="value" id="wsStatus">Checking...</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Active Chatbot:</span>
                        <span class="value" id="activeChatbot">None</span>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="section">
                <h2 class="section-title">Quick Actions</h2>
                <div class="actions">
                    <button class="btn btn-primary" id="connectBtn">
                        <span class="btn-icon">🔌</span>
                        Connect
                    </button>
                    <button class="btn btn-secondary" id="testBtn">
                        <span class="btn-icon">🧪</span>
                        Test
                    </button>
                    <button class="btn btn-secondary" id="refreshBtn">
                        <span class="btn-icon">🔄</span>
                        Refresh
                    </button>
                </div>
            </section>

            <!-- Chatbot Selection -->
            <section class="section">
                <h2 class="section-title">Active Chatbot</h2>
                <div class="chatbot-selector">
                    <select class="select" id="chatbotSelect">
                        <option value="">Select Chatbot...</option>
                        <option value="chatgpt">ChatGPT</option>
                        <option value="kimi">Kimi</option>
                        <option value="claude">Claude</option>
                        <option value="custom">Custom</option>
                    </select>
                    <button class="btn btn-small" id="setChatbotBtn">Set Active</button>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="section">
                <h2 class="section-title">Recent Activity</h2>
                <div class="activity-log" id="activityLog">
                    <div class="activity-item">
                        <span class="activity-time">--:--</span>
                        <span class="activity-text">Extension loaded</span>
                    </div>
                </div>
            </section>

            <!-- Error Display -->
            <section class="section error-section" id="errorSection" style="display: none;">
                <h2 class="section-title error-title">⚠️ Error</h2>
                <div class="error-message" id="errorMessage"></div>
                <button class="btn btn-small btn-secondary" id="clearErrorBtn">Clear</button>
            </section>
        </main>

        <footer class="footer">
            <div class="footer-links">
                <button class="link-btn" id="optionsBtn">Settings</button>
                <button class="link-btn" id="helpBtn">Help</button>
                <button class="link-btn" id="aboutBtn">About</button>
            </div>
            <div class="version">v1.0.0</div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
