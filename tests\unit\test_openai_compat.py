"""
Universal Chatbot Proxy - OpenAI Compatibility Tests
Unit tests for OpenAI API compatibility layer
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock

from local_api_proxy.openai_compat import OpenAICompatibilityLayer
from local_api_proxy.models import (
    ChatCompletionRequest, ChatCompletionResponse, ModelsResponse,
    Message, Choice, Usage
)
from local_api_proxy.config import Config, ChatbotConfig


class TestOpenAICompatibilityLayer:
    """Test OpenAI compatibility layer."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return Config(
            chatbots={
                "chatgpt": ChatbotConfig(
                    url="https://chat.openai.com",
                    enabled=True,
                    models=["gpt-3.5-turbo", "gpt-4"]
                ),
                "kimi": ChatbotConfig(
                    url="https://kimi.moonshot.cn",
                    enabled=True,
                    models=["kimi"]
                )
            },
            default_chatbot="chatgpt",
            default_model="gpt-3.5-turbo"
        )
    
    @pytest.fixture
    def openai_compat(self, config):
        """Create OpenAI compatibility layer."""
        return OpenAICompatibilityLayer(config)
    
    @pytest.fixture
    def mock_bridge(self):
        """Create mock extension bridge."""
        bridge = AsyncMock()
        bridge.send_message.return_value = {
            "success": True,
            "payload": {
                "content": "Test response from chatbot",
                "finish_reason": "stop"
            }
        }
        return bridge
    
    @pytest.fixture
    def sample_chat_request(self):
        """Create sample chat completion request."""
        return ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[
                Message(role="user", content="Hello, world!")
            ],
            temperature=0.7,
            max_tokens=100
        )


class TestModelListing:
    """Test model listing functionality."""
    
    @pytest.mark.asyncio
    async def test_list_models(self, openai_compat):
        """Test listing available models."""
        response = await openai_compat.list_models()
        
        assert isinstance(response, ModelsResponse)
        assert len(response.data) >= 3  # gpt-3.5-turbo, gpt-4, kimi
        
        model_ids = [model.id for model in response.data]
        assert "gpt-3.5-turbo" in model_ids
        assert "gpt-4" in model_ids
        assert "kimi" in model_ids
    
    @pytest.mark.asyncio
    async def test_list_models_only_enabled(self, config):
        """Test listing only enabled models."""
        # Disable one chatbot
        config.chatbots["kimi"].enabled = False
        
        openai_compat = OpenAICompatibilityLayer(config)
        response = await openai_compat.list_models()
        
        model_ids = [model.id for model in response.data]
        assert "gpt-3.5-turbo" in model_ids
        assert "gpt-4" in model_ids
        assert "kimi" not in model_ids


class TestChatbotSelection:
    """Test chatbot selection logic."""
    
    def test_determine_chatbot_by_model(self, openai_compat):
        """Test determining chatbot by model name."""
        # Test with specific model
        chatbot = openai_compat._determine_chatbot("gpt-4")
        assert chatbot == "chatgpt"
        
        chatbot = openai_compat._determine_chatbot("kimi")
        assert chatbot == "kimi"
    
    def test_determine_chatbot_explicit(self, openai_compat):
        """Test explicit chatbot selection."""
        chatbot = openai_compat._determine_chatbot("gpt-3.5-turbo", "kimi")
        assert chatbot == "kimi"
    
    def test_determine_chatbot_unknown_model(self, openai_compat):
        """Test with unknown model falls back to default."""
        chatbot = openai_compat._determine_chatbot("unknown-model")
        assert chatbot == "chatgpt"  # default chatbot
    
    def test_determine_chatbot_unknown_explicit(self, openai_compat):
        """Test with unknown explicit chatbot raises error."""
        with pytest.raises(ValueError, match="Unknown chatbot"):
            openai_compat._determine_chatbot("gpt-3.5-turbo", "unknown-chatbot")
    
    def test_is_chatbot_available(self, openai_compat):
        """Test chatbot availability check."""
        assert openai_compat._is_chatbot_available("chatgpt") is True
        assert openai_compat._is_chatbot_available("kimi") is True
        assert openai_compat._is_chatbot_available("nonexistent") is False


class TestChatCompletion:
    """Test chat completion functionality."""
    
    @pytest.mark.asyncio
    async def test_create_chat_completion(self, openai_compat, mock_bridge, sample_chat_request):
        """Test creating chat completion."""
        response = await openai_compat.create_chat_completion(sample_chat_request, mock_bridge)
        
        assert isinstance(response, ChatCompletionResponse)
        assert response.model == "gpt-3.5-turbo"
        assert len(response.choices) == 1
        assert response.choices[0].message.role == "assistant"
        assert response.choices[0].message.content == "Test response from chatbot"
        assert response.choices[0].finish_reason == "stop"
        assert isinstance(response.usage, Usage)
    
    @pytest.mark.asyncio
    async def test_create_chat_completion_with_explicit_chatbot(self, openai_compat, mock_bridge):
        """Test chat completion with explicit chatbot selection."""
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[Message(role="user", content="Hello")],
            chatbot="kimi"
        )
        
        response = await openai_compat.create_chat_completion(request, mock_bridge)
        
        assert isinstance(response, ChatCompletionResponse)
        # Verify the correct chatbot was called
        mock_bridge.send_message.assert_called_once()
        call_args = mock_bridge.send_message.call_args[0][0]
        assert call_args["chatbot"] == "kimi"
    
    @pytest.mark.asyncio
    async def test_create_chat_completion_bridge_error(self, openai_compat, sample_chat_request):
        """Test chat completion with bridge error."""
        mock_bridge = AsyncMock()
        mock_bridge.send_message.return_value = {
            "success": False,
            "error": "Connection failed"
        }
        
        with pytest.raises(ValueError, match="Connection failed"):
            await openai_compat.create_chat_completion(sample_chat_request, mock_bridge)
    
    @pytest.mark.asyncio
    async def test_create_chat_completion_unavailable_chatbot(self, openai_compat, mock_bridge):
        """Test chat completion with unavailable chatbot."""
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[Message(role="user", content="Hello")],
            chatbot="disabled-chatbot"
        )
        
        with pytest.raises(ValueError, match="Unknown chatbot"):
            await openai_compat.create_chat_completion(request, mock_bridge)


class TestStreamingCompletion:
    """Test streaming completion functionality."""
    
    @pytest.mark.asyncio
    async def test_create_chat_completion_stream(self, openai_compat, mock_bridge, sample_chat_request):
        """Test creating streaming chat completion."""
        # Mock streaming response
        async def mock_streaming():
            yield {"content": "Hello", "finish_reason": None}
            yield {"content": " world", "finish_reason": None}
            yield {"content": "!", "finish_reason": "stop"}
        
        mock_bridge.send_streaming_message.return_value = mock_streaming()
        
        # Enable streaming
        sample_chat_request.stream = True
        
        chunks = []
        async for chunk in openai_compat.create_chat_completion_stream(sample_chat_request, mock_bridge):
            chunks.append(chunk)
        
        assert len(chunks) > 0
        assert chunks[-1] == "data: [DONE]\n\n"
        
        # Verify streaming chunks contain expected data
        for chunk in chunks[:-1]:  # Exclude [DONE] chunk
            assert chunk.startswith("data: ")
            assert "choices" in chunk or "error" in chunk
    
    @pytest.mark.asyncio
    async def test_streaming_with_error(self, openai_compat, mock_bridge, sample_chat_request):
        """Test streaming with error."""
        # Mock streaming error
        async def mock_streaming_error():
            yield {"content": "Partial", "finish_reason": None}
            raise Exception("Streaming error")
        
        mock_bridge.send_streaming_message.return_value = mock_streaming_error()
        sample_chat_request.stream = True
        
        chunks = []
        async for chunk in openai_compat.create_chat_completion_stream(sample_chat_request, mock_bridge):
            chunks.append(chunk)
        
        # Should contain error chunk
        error_chunk = next((chunk for chunk in chunks if "error" in chunk), None)
        assert error_chunk is not None


class TestLegacyCompletion:
    """Test legacy text completion functionality."""
    
    @pytest.mark.asyncio
    async def test_create_completion(self, openai_compat, mock_bridge):
        """Test creating legacy text completion."""
        # Create a chat request (internal conversion)
        chat_request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[Message(role="user", content="Hello, world!")],
            max_tokens=100
        )
        
        response = await openai_compat.create_completion(chat_request, mock_bridge)
        
        assert response.id.startswith("cmpl-")
        assert response.object == "text_completion"
        assert response.model == "gpt-3.5-turbo"
        assert len(response.choices) == 1
        assert response.choices[0].text == "Test response from chatbot"
    
    @pytest.mark.asyncio
    async def test_create_completion_stream(self, openai_compat, mock_bridge):
        """Test creating streaming legacy completion."""
        # Mock streaming response
        async def mock_streaming():
            yield {"content": "Hello", "finish_reason": None}
            yield {"content": " world", "finish_reason": "stop"}
        
        mock_bridge.send_streaming_message.return_value = mock_streaming()
        
        chat_request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[Message(role="user", content="Hello")],
            stream=True
        )
        
        chunks = []
        async for chunk in openai_compat.create_completion_stream(chat_request, mock_bridge):
            chunks.append(chunk)
        
        assert len(chunks) > 0
        assert chunks[-1] == "data: [DONE]\n\n"


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_calculate_usage(self, openai_compat):
        """Test token usage calculation."""
        messages = [
            Message(role="user", content="Hello, world!"),
            Message(role="assistant", content="Hi there!")
        ]
        response = Message(role="assistant", content="Test response")
        
        usage = openai_compat._calculate_usage(messages, response)
        
        assert isinstance(usage, Usage)
        assert usage.prompt_tokens > 0
        assert usage.completion_tokens > 0
        assert usage.total_tokens == usage.prompt_tokens + usage.completion_tokens
    
    def test_format_chat_completion_response(self, openai_compat):
        """Test formatting chat completion response."""
        completion_id = "test-completion-id"
        model = "gpt-3.5-turbo"
        response_data = {
            "content": "Test response",
            "finish_reason": "stop"
        }
        original_messages = [Message(role="user", content="Hello")]
        
        response = openai_compat._format_chat_completion_response(
            completion_id, model, response_data, original_messages
        )
        
        assert isinstance(response, ChatCompletionResponse)
        assert response.id == completion_id
        assert response.model == model
        assert response.choices[0].message.content == "Test response"
        assert response.choices[0].finish_reason == "stop"


class TestErrorHandling:
    """Test error handling in OpenAI compatibility layer."""
    
    @pytest.mark.asyncio
    async def test_send_to_chatbot_error(self, openai_compat, sample_chat_request):
        """Test error handling in send_to_chatbot."""
        mock_bridge = AsyncMock()
        mock_bridge.send_message.side_effect = Exception("Bridge connection failed")
        
        with pytest.raises(Exception, match="Bridge connection failed"):
            await openai_compat._send_to_chatbot(
                mock_bridge, "chatgpt", sample_chat_request, "test-id"
            )
    
    @pytest.mark.asyncio
    async def test_send_streaming_to_chatbot_error(self, openai_compat, sample_chat_request):
        """Test error handling in streaming send_to_chatbot."""
        mock_bridge = AsyncMock()
        
        async def mock_streaming_error():
            raise Exception("Streaming failed")
        
        mock_bridge.send_streaming_message.return_value = mock_streaming_error()
        
        with pytest.raises(Exception, match="Streaming failed"):
            async for _ in openai_compat._send_streaming_to_chatbot(
                mock_bridge, "chatgpt", sample_chat_request, "test-id"
            ):
                pass
