/**
 * Universal Chatbot Proxy - Utility Functions
 * Shared utilities for the Chrome extension
 */

// Extension configuration
const CONFIG = {
  API_BASE_URL: 'http://localhost:11435',
  WEBSOCKET_URL: 'ws://localhost:11436',
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  TYPING_DELAY: { min: 50, max: 150 },
  ACTION_DELAY: { min: 1000, max: 3000 },
  RESPONSE_TIMEOUT: 30000,
};

// Logging utility
class Logger {
  constructor(prefix = 'ChatbotProxy') {
    this.prefix = prefix;
    this.debugMode = false;
  }

  setDebugMode(enabled) {
    this.debugMode = enabled;
  }

  log(level, message, ...args) {
    if (level === 'debug' && !this.debugMode) return;
    
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${this.prefix}] [${level.toUpperCase()}] ${message}`;
    
    console[level](logMessage, ...args);
  }

  debug(message, ...args) { this.log('debug', message, ...args); }
  info(message, ...args) { this.log('info', message, ...args); }
  warn(message, ...args) { this.log('warn', message, ...args); }
  error(message, ...args) { this.log('error', message, ...args); }
}

// Global logger instance
const logger = new Logger();

// Utility functions
const Utils = {
  /**
   * Sleep for a specified duration
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Generate random delay within range
   */
  randomDelay(min = 1000, max = 3000) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * Retry function with exponential backoff
   */
  async retry(fn, attempts = CONFIG.RETRY_ATTEMPTS, delay = CONFIG.RETRY_DELAY) {
    for (let i = 0; i < attempts; i++) {
      try {
        return await fn();
      } catch (error) {
        if (i === attempts - 1) throw error;
        
        const backoffDelay = delay * Math.pow(2, i);
        logger.warn(`Attempt ${i + 1} failed, retrying in ${backoffDelay}ms:`, error.message);
        await this.sleep(backoffDelay);
      }
    }
  },

  /**
   * Wait for element to appear in DOM
   */
  async waitForElement(selector, timeout = 10000, parent = document) {
    return new Promise((resolve, reject) => {
      const element = parent.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = parent.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(parent, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  },

  /**
   * Simulate human-like typing
   */
  async typeText(element, text, options = {}) {
    const { 
      clearFirst = true, 
      triggerEvents = true,
      typingDelay = CONFIG.TYPING_DELAY 
    } = options;

    if (clearFirst) {
      element.value = '';
      element.textContent = '';
    }

    if (triggerEvents) {
      element.focus();
      element.dispatchEvent(new Event('focus', { bubbles: true }));
    }

    for (const char of text) {
      if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
        element.value += char;
      } else {
        element.textContent += char;
      }

      if (triggerEvents) {
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new KeyboardEvent('keydown', { 
          key: char, 
          bubbles: true 
        }));
      }

      // Random typing delay
      const delay = this.randomDelay(typingDelay.min, typingDelay.max);
      await this.sleep(delay);
    }

    if (triggerEvents) {
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
    }
  },

  /**
   * Simulate click with human-like behavior
   */
  async clickElement(element, options = {}) {
    const { delay = true } = options;

    if (delay) {
      await this.sleep(this.randomDelay(500, 1500));
    }

    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await this.sleep(200);

    // Simulate mouse events
    const rect = element.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;

    const mouseEvents = ['mousedown', 'mouseup', 'click'];
    for (const eventType of mouseEvents) {
      const event = new MouseEvent(eventType, {
        bubbles: true,
        cancelable: true,
        clientX: x,
        clientY: y
      });
      element.dispatchEvent(event);
      await this.sleep(50);
    }
  },

  /**
   * Extract text content from element
   */
  extractText(element) {
    if (!element) return '';

    // Remove script and style elements
    const clone = element.cloneNode(true);
    const scripts = clone.querySelectorAll('script, style');
    scripts.forEach(el => el.remove());

    return clone.textContent || clone.innerText || '';
  },

  /**
   * Check if element is visible
   */
  isElementVisible(element) {
    if (!element) return false;

    const style = window.getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0' &&
           element.offsetWidth > 0 && 
           element.offsetHeight > 0;
  },

  /**
   * Generate unique ID
   */
  generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  /**
   * Debounce function
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * Throttle function
   */
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * Deep clone object
   */
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  },

  /**
   * Validate URL
   */
  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  },

  /**
   * Get current page info
   */
  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      domain: window.location.hostname,
      pathname: window.location.pathname,
      timestamp: Date.now()
    };
  }
};

// Message types for communication
const MessageTypes = {
  // From content script to background
  CONTENT_READY: 'content_ready',
  RESPONSE_RECEIVED: 'response_received',
  ERROR_OCCURRED: 'error_occurred',
  STATUS_UPDATE: 'status_update',

  // From background to content script
  SEND_MESSAGE: 'send_message',
  GET_STATUS: 'get_status',
  CONFIGURE: 'configure',
  RESET: 'reset',

  // WebSocket messages
  WS_CONNECT: 'ws_connect',
  WS_DISCONNECT: 'ws_disconnect',
  WS_MESSAGE: 'ws_message',
  WS_ERROR: 'ws_error'
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { Utils, Logger, CONFIG, MessageTypes };
} else {
  // Browser environment
  window.ChatbotProxyUtils = { Utils, Logger, CONFIG, MessageTypes, logger };
}
