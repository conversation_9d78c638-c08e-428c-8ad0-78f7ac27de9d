"""
Universal Chatbot Proxy - OpenAI Compatibility Layer
Handles conversion between OpenAI API format and chatbot responses
"""

import asyncio
import json
import logging
import time
import uuid
from typing import AsyncGenerator, Dict, List, Optional

from .models import (
    ChatCompletionRequest, ChatCompletionResponse, ChatCompletionStreamResponse,
    CompletionRequest, CompletionResponse, ModelsResponse, Model,
    Choice, StreamChoice, Delta, Usage, Message
)
from .config import Config
from .bridge_client import ExtensionBridge


class OpenAICompatibilityLayer:
    """Handles OpenAI API compatibility"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    async def list_models(self) -> ModelsResponse:
        """List available models based on chatbot configurations"""
        models = []
        
        for chatbot_name, chatbot_config in self.config.chatbots.items():
            if not chatbot_config.enabled:
                continue
            
            for model_name in chatbot_config.models:
                models.append(Model(
                    id=model_name,
                    owned_by=f"chatbot-{chatbot_name}",
                    root=model_name,
                    parent=None
                ))
        
        return ModelsResponse(data=models)
    
    async def create_chat_completion(
        self, 
        request: ChatCompletionRequest, 
        bridge: ExtensionBridge
    ) -> ChatCompletionResponse:
        """Create a chat completion"""
        
        # Determine which chatbot to use
        chatbot_name = self._determine_chatbot(request.model, request.chatbot)
        
        # Validate chatbot availability
        if not self._is_chatbot_available(chatbot_name):
            raise ValueError(f"Chatbot '{chatbot_name}' is not available")
        
        # Generate completion ID
        completion_id = f"chatcmpl-{uuid.uuid4().hex[:24]}"
        
        try:
            # Send request to extension
            response = await self._send_to_chatbot(
                bridge, chatbot_name, request, completion_id
            )
            
            # Convert response to OpenAI format
            return self._format_chat_completion_response(
                completion_id, request.model, response, request.messages
            )
            
        except Exception as e:
            self.logger.error(f"Chat completion error: {e}")
            raise
    
    async def create_chat_completion_stream(
        self, 
        request: ChatCompletionRequest, 
        bridge: ExtensionBridge
    ) -> AsyncGenerator[str, None]:
        """Create a streaming chat completion"""
        
        # Determine which chatbot to use
        chatbot_name = self._determine_chatbot(request.model, request.chatbot)
        
        # Validate chatbot availability
        if not self._is_chatbot_available(chatbot_name):
            raise ValueError(f"Chatbot '{chatbot_name}' is not available")
        
        # Generate completion ID
        completion_id = f"chatcmpl-{uuid.uuid4().hex[:24]}"
        
        try:
            # Send streaming request to extension
            async for chunk in self._send_streaming_to_chatbot(
                bridge, chatbot_name, request, completion_id
            ):
                # Format as OpenAI streaming response
                stream_response = ChatCompletionStreamResponse(
                    id=completion_id,
                    model=request.model,
                    choices=[StreamChoice(
                        index=0,
                        delta=Delta(content=chunk.get('content', '')),
                        finish_reason=chunk.get('finish_reason')
                    )]
                )
                
                yield f"data: {stream_response.json()}\n\n"
            
            # Send final chunk
            final_response = ChatCompletionStreamResponse(
                id=completion_id,
                model=request.model,
                choices=[StreamChoice(
                    index=0,
                    delta=Delta(),
                    finish_reason="stop"
                )]
            )
            
            yield f"data: {final_response.json()}\n\n"
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            self.logger.error(f"Streaming chat completion error: {e}")
            # Send error in streaming format
            error_response = {
                "error": {
                    "message": str(e),
                    "type": "server_error",
                    "code": 500
                }
            }
            yield f"data: {json.dumps(error_response)}\n\n"
    
    async def create_completion(
        self, 
        request: ChatCompletionRequest, 
        bridge: ExtensionBridge
    ) -> CompletionResponse:
        """Create a text completion (legacy format)"""
        
        # Convert chat completion to legacy format
        chat_response = await self.create_chat_completion(request, bridge)
        
        # Extract text from chat response
        text = ""
        if chat_response.choices and chat_response.choices[0].message:
            text = chat_response.choices[0].message.content
        
        # Create legacy completion response
        return CompletionResponse(
            id=chat_response.id.replace("chatcmpl-", "cmpl-"),
            model=chat_response.model,
            choices=[Choice(
                index=0,
                text=text,
                finish_reason=chat_response.choices[0].finish_reason if chat_response.choices else "stop"
            )],
            usage=chat_response.usage
        )
    
    async def create_completion_stream(
        self, 
        request: ChatCompletionRequest, 
        bridge: ExtensionBridge
    ) -> AsyncGenerator[str, None]:
        """Create a streaming text completion (legacy format)"""
        
        completion_id = f"cmpl-{uuid.uuid4().hex[:24]}"
        
        async for chunk_data in self.create_chat_completion_stream(request, bridge):
            if chunk_data.startswith("data: [DONE]"):
                yield chunk_data
                break
            elif chunk_data.startswith("data: "):
                try:
                    # Parse the streaming response
                    data = json.loads(chunk_data[6:])
                    
                    if "choices" in data and data["choices"]:
                        choice = data["choices"][0]
                        delta = choice.get("delta", {})
                        content = delta.get("content", "")
                        
                        # Convert to legacy format
                        legacy_response = {
                            "id": completion_id,
                            "object": "text_completion",
                            "created": int(time.time()),
                            "model": request.model,
                            "choices": [{
                                "index": 0,
                                "text": content,
                                "finish_reason": choice.get("finish_reason")
                            }]
                        }
                        
                        yield f"data: {json.dumps(legacy_response)}\n\n"
                
                except json.JSONDecodeError:
                    continue
    
    def _determine_chatbot(self, model: str, explicit_chatbot: Optional[str] = None) -> str:
        """Determine which chatbot to use based on model or explicit selection"""
        
        if explicit_chatbot:
            if explicit_chatbot in self.config.chatbots:
                return explicit_chatbot
            else:
                raise ValueError(f"Unknown chatbot: {explicit_chatbot}")
        
        # Find chatbot that supports this model
        for chatbot_name, chatbot_config in self.config.chatbots.items():
            if chatbot_config.enabled and model in chatbot_config.models:
                return chatbot_name
        
        # Fallback to default chatbot
        if self.config.default_chatbot in self.config.chatbots:
            return self.config.default_chatbot
        
        # If no suitable chatbot found, use the first available one
        for chatbot_name, chatbot_config in self.config.chatbots.items():
            if chatbot_config.enabled:
                return chatbot_name
        
        raise ValueError(f"No available chatbot for model: {model}")
    
    def _is_chatbot_available(self, chatbot_name: str) -> bool:
        """Check if chatbot is available"""
        chatbot_config = self.config.chatbots.get(chatbot_name)
        return chatbot_config is not None and chatbot_config.enabled
    
    async def _send_to_chatbot(
        self, 
        bridge: ExtensionBridge, 
        chatbot_name: str, 
        request: ChatCompletionRequest,
        completion_id: str
    ) -> Dict:
        """Send request to chatbot via extension bridge"""
        
        # Prepare message for extension
        message = {
            "id": completion_id,
            "type": "chat_completion",
            "chatbot": chatbot_name,
            "payload": {
                "messages": [msg.dict() for msg in request.messages],
                "model": request.model,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "stream": False,
                "timeout": request.timeout
            }
        }
        
        # Send to extension and wait for response
        response = await bridge.send_message(message)
        
        if not response.get("success"):
            raise ValueError(response.get("error", "Unknown error from chatbot"))
        
        return response.get("payload", {})
    
    async def _send_streaming_to_chatbot(
        self, 
        bridge: ExtensionBridge, 
        chatbot_name: str, 
        request: ChatCompletionRequest,
        completion_id: str
    ) -> AsyncGenerator[Dict, None]:
        """Send streaming request to chatbot via extension bridge"""
        
        # Prepare message for extension
        message = {
            "id": completion_id,
            "type": "chat_completion",
            "chatbot": chatbot_name,
            "payload": {
                "messages": [msg.dict() for msg in request.messages],
                "model": request.model,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "stream": True,
                "timeout": request.timeout
            }
        }
        
        # Send to extension and yield streaming responses
        async for chunk in bridge.send_streaming_message(message):
            yield chunk
    
    def _format_chat_completion_response(
        self, 
        completion_id: str, 
        model: str, 
        response: Dict,
        original_messages: List[Message]
    ) -> ChatCompletionResponse:
        """Format response as OpenAI chat completion"""
        
        # Extract response content
        content = response.get("content", "")
        finish_reason = response.get("finish_reason", "stop")
        
        # Create response message
        response_message = Message(
            role="assistant",
            content=content
        )
        
        # Calculate token usage (approximation)
        usage = self._calculate_usage(original_messages, response_message)
        
        return ChatCompletionResponse(
            id=completion_id,
            model=model,
            choices=[Choice(
                index=0,
                message=response_message,
                finish_reason=finish_reason
            )],
            usage=usage
        )
    
    def _calculate_usage(self, messages: List[Message], response: Message) -> Usage:
        """Calculate approximate token usage"""
        
        # Simple approximation: 1 token ≈ 4 characters
        prompt_text = " ".join(msg.content for msg in messages)
        prompt_tokens = max(1, len(prompt_text) // 4)
        
        completion_tokens = max(1, len(response.content) // 4)
        total_tokens = prompt_tokens + completion_tokens
        
        return Usage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens
        )
