"""
Universal Chatbot Proxy - Error Handling Utilities
Comprehensive error handling, retry mechanisms, and recovery strategies
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Callable, Dict, List, Optional, Type, Union
from dataclasses import dataclass
from enum import Enum
import functools


class ErrorType(Enum):
    """Types of errors that can occur"""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    AUTHENTICATION_ERROR = "authentication_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    CHATBOT_ERROR = "chatbot_error"
    EXTENSION_ERROR = "extension_error"
    VALIDATION_ERROR = "validation_error"
    CONFIGURATION_ERROR = "configuration_error"
    INTERNAL_ERROR = "internal_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """Detailed error information"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: float = None
    component: Optional[str] = None
    operation: Optional[str] = None
    retry_count: int = 0
    recoverable: bool = True
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class RetryConfig:
    """Configuration for retry mechanisms"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    retry_on: List[ErrorType] = None
    
    def __post_init__(self):
        if self.retry_on is None:
            self.retry_on = [
                ErrorType.NETWORK_ERROR,
                ErrorType.TIMEOUT_ERROR,
                ErrorType.RATE_LIMIT_ERROR,
                ErrorType.INTERNAL_ERROR
            ]


class ProxyError(Exception):
    """Base exception for proxy errors"""
    
    def __init__(self, error_info: ErrorInfo):
        self.error_info = error_info
        super().__init__(error_info.message)


class NetworkError(ProxyError):
    """Network-related errors"""
    pass


class TimeoutError(ProxyError):
    """Timeout errors"""
    pass


class ChatbotError(ProxyError):
    """Chatbot-specific errors"""
    pass


class ExtensionError(ProxyError):
    """Extension-related errors"""
    pass


class ValidationError(ProxyError):
    """Validation errors"""
    pass


class ConfigurationError(ProxyError):
    """Configuration errors"""
    pass


class ErrorHandler:
    """Centralized error handling and recovery"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_history: List[ErrorInfo] = []
        self.max_history_size = 1000
        self.error_callbacks: Dict[ErrorType, List[Callable]] = {}
        
        # Error statistics
        self.error_counts: Dict[ErrorType, int] = {}
        self.recovery_counts: Dict[ErrorType, int] = {}
        
    def register_error_callback(self, error_type: ErrorType, callback: Callable):
        """Register callback for specific error type"""
        if error_type not in self.error_callbacks:
            self.error_callbacks[error_type] = []
        self.error_callbacks[error_type].append(callback)
    
    def handle_error(self, error: Exception, component: str = None, operation: str = None) -> ErrorInfo:
        """Handle and classify an error"""
        error_info = self._classify_error(error, component, operation)
        
        # Log the error
        self._log_error(error_info)
        
        # Store in history
        self._store_error(error_info)
        
        # Update statistics
        self._update_statistics(error_info)
        
        # Execute callbacks
        self._execute_callbacks(error_info)
        
        return error_info
    
    def _classify_error(self, error: Exception, component: str = None, operation: str = None) -> ErrorInfo:
        """Classify error and determine severity"""
        error_type = ErrorType.UNKNOWN_ERROR
        severity = ErrorSeverity.MEDIUM
        recoverable = True
        
        # Classify based on exception type and message
        if isinstance(error, (ConnectionError, OSError)):
            error_type = ErrorType.NETWORK_ERROR
            severity = ErrorSeverity.HIGH
        elif isinstance(error, asyncio.TimeoutError):
            error_type = ErrorType.TIMEOUT_ERROR
            severity = ErrorSeverity.MEDIUM
        elif isinstance(error, PermissionError):
            error_type = ErrorType.AUTHENTICATION_ERROR
            severity = ErrorSeverity.HIGH
            recoverable = False
        elif "rate limit" in str(error).lower():
            error_type = ErrorType.RATE_LIMIT_ERROR
            severity = ErrorSeverity.MEDIUM
        elif "validation" in str(error).lower():
            error_type = ErrorType.VALIDATION_ERROR
            severity = ErrorSeverity.LOW
            recoverable = False
        elif "configuration" in str(error).lower() or "config" in str(error).lower():
            error_type = ErrorType.CONFIGURATION_ERROR
            severity = ErrorSeverity.HIGH
            recoverable = False
        
        # Component-specific classification
        if component:
            if "extension" in component.lower():
                if error_type == ErrorType.UNKNOWN_ERROR:
                    error_type = ErrorType.EXTENSION_ERROR
            elif "chatbot" in component.lower():
                if error_type == ErrorType.UNKNOWN_ERROR:
                    error_type = ErrorType.CHATBOT_ERROR
        
        return ErrorInfo(
            error_type=error_type,
            severity=severity,
            message=str(error),
            details={
                "exception_type": type(error).__name__,
                "traceback": traceback.format_exc()
            },
            component=component,
            operation=operation,
            recoverable=recoverable
        )
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error with appropriate level"""
        log_message = f"[{error_info.component or 'Unknown'}] {error_info.message}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, extra={"error_info": error_info})
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, extra={"error_info": error_info})
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message, extra={"error_info": error_info})
        else:
            self.logger.info(log_message, extra={"error_info": error_info})
    
    def _store_error(self, error_info: ErrorInfo):
        """Store error in history"""
        self.error_history.append(error_info)
        
        # Trim history if too large
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
    
    def _update_statistics(self, error_info: ErrorInfo):
        """Update error statistics"""
        self.error_counts[error_info.error_type] = self.error_counts.get(error_info.error_type, 0) + 1
    
    def _execute_callbacks(self, error_info: ErrorInfo):
        """Execute registered callbacks for error type"""
        callbacks = self.error_callbacks.get(error_info.error_type, [])
        for callback in callbacks:
            try:
                callback(error_info)
            except Exception as e:
                self.logger.error(f"Error callback failed: {e}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        total_errors = sum(self.error_counts.values())
        
        return {
            "total_errors": total_errors,
            "error_counts": dict(self.error_counts),
            "recovery_counts": dict(self.recovery_counts),
            "recent_errors": len([e for e in self.error_history if time.time() - e.timestamp < 3600]),
            "error_rate": total_errors / max(1, len(self.error_history)) if self.error_history else 0
        }
    
    def get_recent_errors(self, limit: int = 10) -> List[ErrorInfo]:
        """Get recent errors"""
        return sorted(self.error_history, key=lambda x: x.timestamp, reverse=True)[:limit]


class RetryManager:
    """Manages retry logic with exponential backoff"""
    
    def __init__(self, config: RetryConfig = None):
        self.config = config or RetryConfig()
        self.logger = logging.getLogger(__name__)
    
    async def retry_async(self, func: Callable, *args, **kwargs) -> Any:
        """Retry async function with exponential backoff"""
        last_error = None
        
        for attempt in range(self.config.max_attempts):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_error = e
                error_info = ErrorHandler().handle_error(e, operation=func.__name__)
                
                if not self._should_retry(error_info, attempt):
                    break
                
                if attempt < self.config.max_attempts - 1:
                    delay = self._calculate_delay(attempt)
                    self.logger.info(f"Retrying {func.__name__} in {delay:.2f}s (attempt {attempt + 1}/{self.config.max_attempts})")
                    await asyncio.sleep(delay)
        
        # All retries exhausted
        raise last_error
    
    def retry_sync(self, func: Callable, *args, **kwargs) -> Any:
        """Retry sync function with exponential backoff"""
        last_error = None
        
        for attempt in range(self.config.max_attempts):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_error = e
                error_info = ErrorHandler().handle_error(e, operation=func.__name__)
                
                if not self._should_retry(error_info, attempt):
                    break
                
                if attempt < self.config.max_attempts - 1:
                    delay = self._calculate_delay(attempt)
                    self.logger.info(f"Retrying {func.__name__} in {delay:.2f}s (attempt {attempt + 1}/{self.config.max_attempts})")
                    time.sleep(delay)
        
        # All retries exhausted
        raise last_error
    
    def _should_retry(self, error_info: ErrorInfo, attempt: int) -> bool:
        """Determine if error should be retried"""
        if attempt >= self.config.max_attempts - 1:
            return False
        
        if not error_info.recoverable:
            return False
        
        return error_info.error_type in self.config.retry_on
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        delay = self.config.base_delay * (self.config.exponential_base ** attempt)
        delay = min(delay, self.config.max_delay)
        
        if self.config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay


def retry_on_error(config: RetryConfig = None):
    """Decorator for automatic retry on error"""
    retry_manager = RetryManager(config)
    
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await retry_manager.retry_async(func, *args, **kwargs)
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                return retry_manager.retry_sync(func, *args, **kwargs)
            return sync_wrapper
    
    return decorator


class CircuitBreaker:
    """Circuit breaker pattern for preventing cascading failures"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
        self.logger = logging.getLogger(__name__)
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Call function through circuit breaker"""
        if self.state == "open":
            if time.time() - self.last_failure_time < self.recovery_timeout:
                raise Exception("Circuit breaker is open")
            else:
                self.state = "half-open"
                self.logger.info("Circuit breaker entering half-open state")
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            
            if self.state == "half-open":
                self.state = "closed"
                self.failure_count = 0
                self.logger.info("Circuit breaker closed - service recovered")
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "open"
                self.logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
            
            raise


# Global error handler instance
_error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """Get global error handler instance"""
    return _error_handler
