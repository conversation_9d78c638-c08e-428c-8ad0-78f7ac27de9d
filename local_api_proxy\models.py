"""
Universal Chatbot Proxy - Data Models
Pydantic models for API requests and responses
"""

from typing import List, Optional, Dict, Any, Union, Literal
from pydantic import BaseModel, Field, validator
import time


# Base models
class Message(BaseModel):
    """Chat message model"""
    role: Literal["system", "user", "assistant"] = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    name: Optional[str] = Field(None, description="Message author name")


class Usage(BaseModel):
    """Token usage information"""
    prompt_tokens: int = Field(..., description="Number of tokens in the prompt")
    completion_tokens: int = Field(..., description="Number of tokens in the completion")
    total_tokens: int = Field(..., description="Total number of tokens")


class Choice(BaseModel):
    """Completion choice"""
    index: int = Field(..., description="Choice index")
    message: Optional[Message] = Field(None, description="Message content")
    text: Optional[str] = Field(None, description="Text content (for legacy completions)")
    finish_reason: Optional[Literal["stop", "length", "content_filter", "null"]] = Field(
        None, description="Reason for completion finish"
    )


class Delta(BaseModel):
    """Streaming delta"""
    role: Optional[str] = Field(None, description="Message role")
    content: Optional[str] = Field(None, description="Content delta")


class StreamChoice(BaseModel):
    """Streaming choice"""
    index: int = Field(..., description="Choice index")
    delta: Delta = Field(..., description="Content delta")
    finish_reason: Optional[Literal["stop", "length", "content_filter", "null"]] = Field(
        None, description="Reason for completion finish"
    )


# Request models
class ChatCompletionRequest(BaseModel):
    """Chat completion request"""
    model: str = Field(..., description="Model to use for completion")
    messages: List[Message] = Field(..., description="List of messages")
    temperature: Optional[float] = Field(0.7, ge=0, le=2, description="Sampling temperature")
    top_p: Optional[float] = Field(1.0, ge=0, le=1, description="Nucleus sampling parameter")
    n: Optional[int] = Field(1, ge=1, le=10, description="Number of completions to generate")
    stream: Optional[bool] = Field(False, description="Enable streaming")
    stop: Optional[Union[str, List[str]]] = Field(None, description="Stop sequences")
    max_tokens: Optional[int] = Field(None, ge=1, description="Maximum tokens to generate")
    presence_penalty: Optional[float] = Field(0, ge=-2, le=2, description="Presence penalty")
    frequency_penalty: Optional[float] = Field(0, ge=-2, le=2, description="Frequency penalty")
    logit_bias: Optional[Dict[str, float]] = Field(None, description="Logit bias")
    user: Optional[str] = Field(None, description="User identifier")
    
    # Extension-specific parameters
    chatbot: Optional[str] = Field(None, description="Specific chatbot to use")
    timeout: Optional[int] = Field(30, ge=5, le=300, description="Request timeout in seconds")
    
    @validator('messages')
    def validate_messages(cls, v):
        if not v:
            raise ValueError("Messages list cannot be empty")
        return v


class CompletionRequest(BaseModel):
    """Text completion request (legacy)"""
    model: str = Field(..., description="Model to use for completion")
    prompt: str = Field(..., description="Prompt text")
    suffix: Optional[str] = Field(None, description="Suffix text")
    max_tokens: Optional[int] = Field(16, ge=1, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(1.0, ge=0, le=2, description="Sampling temperature")
    top_p: Optional[float] = Field(1.0, ge=0, le=1, description="Nucleus sampling parameter")
    n: Optional[int] = Field(1, ge=1, le=10, description="Number of completions to generate")
    stream: Optional[bool] = Field(False, description="Enable streaming")
    logprobs: Optional[int] = Field(None, ge=0, le=5, description="Log probabilities")
    echo: Optional[bool] = Field(False, description="Echo the prompt")
    stop: Optional[Union[str, List[str]]] = Field(None, description="Stop sequences")
    presence_penalty: Optional[float] = Field(0, ge=-2, le=2, description="Presence penalty")
    frequency_penalty: Optional[float] = Field(0, ge=-2, le=2, description="Frequency penalty")
    best_of: Optional[int] = Field(1, ge=1, description="Best of N generations")
    logit_bias: Optional[Dict[str, float]] = Field(None, description="Logit bias")
    user: Optional[str] = Field(None, description="User identifier")


# Response models
class ChatCompletionResponse(BaseModel):
    """Chat completion response"""
    id: str = Field(..., description="Completion ID")
    object: Literal["chat.completion"] = Field("chat.completion", description="Object type")
    created: int = Field(default_factory=lambda: int(time.time()), description="Creation timestamp")
    model: str = Field(..., description="Model used")
    choices: List[Choice] = Field(..., description="Completion choices")
    usage: Usage = Field(..., description="Token usage")
    system_fingerprint: Optional[str] = Field(None, description="System fingerprint")


class ChatCompletionStreamResponse(BaseModel):
    """Chat completion streaming response"""
    id: str = Field(..., description="Completion ID")
    object: Literal["chat.completion.chunk"] = Field("chat.completion.chunk", description="Object type")
    created: int = Field(default_factory=lambda: int(time.time()), description="Creation timestamp")
    model: str = Field(..., description="Model used")
    choices: List[StreamChoice] = Field(..., description="Streaming choices")
    system_fingerprint: Optional[str] = Field(None, description="System fingerprint")


class CompletionResponse(BaseModel):
    """Text completion response"""
    id: str = Field(..., description="Completion ID")
    object: Literal["text_completion"] = Field("text_completion", description="Object type")
    created: int = Field(default_factory=lambda: int(time.time()), description="Creation timestamp")
    model: str = Field(..., description="Model used")
    choices: List[Choice] = Field(..., description="Completion choices")
    usage: Usage = Field(..., description="Token usage")


class Model(BaseModel):
    """Model information"""
    id: str = Field(..., description="Model ID")
    object: Literal["model"] = Field("model", description="Object type")
    created: int = Field(default_factory=lambda: int(time.time()), description="Creation timestamp")
    owned_by: str = Field("universal-chatbot-proxy", description="Model owner")
    permission: List[Dict[str, Any]] = Field(default_factory=list, description="Model permissions")
    root: Optional[str] = Field(None, description="Root model")
    parent: Optional[str] = Field(None, description="Parent model")


class ModelsResponse(BaseModel):
    """Models list response"""
    object: Literal["list"] = Field("list", description="Object type")
    data: List[Model] = Field(..., description="List of models")


class HealthResponse(BaseModel):
    """Health check response"""
    status: Literal["healthy", "unhealthy"] = Field(..., description="Service status")
    version: str = Field(..., description="Service version")
    uptime: int = Field(..., description="Uptime in seconds")
    extension_connected: bool = Field(..., description="Extension connection status")
    active_chatbots: List[str] = Field(..., description="List of active chatbots")
    timestamp: Optional[int] = Field(default_factory=lambda: int(time.time()), description="Response timestamp")


class ErrorDetail(BaseModel):
    """Error detail"""
    message: str = Field(..., description="Error message")
    type: str = Field(..., description="Error type")
    param: Optional[str] = Field(None, description="Parameter that caused the error")
    code: Optional[Union[str, int]] = Field(None, description="Error code")


class ErrorResponse(BaseModel):
    """Error response"""
    error: ErrorDetail = Field(..., description="Error details")


# Extension-specific models
class ExtensionMessage(BaseModel):
    """Message for extension communication"""
    id: str = Field(..., description="Message ID")
    type: str = Field(..., description="Message type")
    payload: Dict[str, Any] = Field(..., description="Message payload")
    timestamp: int = Field(default_factory=lambda: int(time.time()), description="Message timestamp")


class ExtensionResponse(BaseModel):
    """Response from extension"""
    id: str = Field(..., description="Message ID")
    success: bool = Field(..., description="Success status")
    payload: Optional[Dict[str, Any]] = Field(None, description="Response payload")
    error: Optional[str] = Field(None, description="Error message")
    timestamp: int = Field(default_factory=lambda: int(time.time()), description="Response timestamp")


class ChatbotStatus(BaseModel):
    """Chatbot status information"""
    name: str = Field(..., description="Chatbot name")
    url: str = Field(..., description="Chatbot URL")
    connected: bool = Field(..., description="Connection status")
    ready: bool = Field(..., description="Ready status")
    last_activity: Optional[int] = Field(None, description="Last activity timestamp")
    error: Optional[str] = Field(None, description="Last error message")


class ProxyStatus(BaseModel):
    """Proxy service status"""
    server_status: Literal["running", "stopped", "error"] = Field(..., description="Server status")
    extension_connected: bool = Field(..., description="Extension connection status")
    active_chatbots: List[ChatbotStatus] = Field(..., description="Active chatbots")
    total_requests: int = Field(0, description="Total requests processed")
    successful_requests: int = Field(0, description="Successful requests")
    failed_requests: int = Field(0, description="Failed requests")
    uptime: int = Field(..., description="Uptime in seconds")
    version: str = Field(..., description="Service version")


# Configuration models
class ChatbotConfig(BaseModel):
    """Chatbot configuration"""
    name: str = Field(..., description="Chatbot name")
    url: str = Field(..., description="Chatbot URL")
    enabled: bool = Field(True, description="Whether chatbot is enabled")
    models: List[str] = Field(..., description="Supported model names")
    timeout: int = Field(30, ge=5, le=300, description="Request timeout")
    max_retries: int = Field(3, ge=1, le=10, description="Maximum retry attempts")
    selectors: Dict[str, str] = Field(..., description="DOM selectors")
    
    @validator('url')
    def validate_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("URL must start with http:// or https://")
        return v


class ProxyConfig(BaseModel):
    """Proxy configuration"""
    api_url: str = Field("http://localhost:11435", description="API server URL")
    websocket_url: str = Field("ws://localhost:11436", description="WebSocket URL")
    default_chatbot: str = Field("chatgpt", description="Default chatbot")
    default_model: str = Field("gpt-3.5-turbo", description="Default model")
    request_timeout: int = Field(30, ge=5, le=300, description="Default request timeout")
    max_retries: int = Field(3, ge=1, le=10, description="Default max retries")
    debug_mode: bool = Field(False, description="Enable debug mode")
    chatbots: Dict[str, ChatbotConfig] = Field(default_factory=dict, description="Chatbot configurations")
