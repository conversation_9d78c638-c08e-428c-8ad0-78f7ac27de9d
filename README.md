# 🤖 Universal Chatbot Proxy

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![Chrome Extension](https://img.shields.io/badge/chrome-extension-green.svg)](https://developer.chrome.com/docs/extensions/)

A universal proxy that makes any web-based chatbot (ChatGPT, Kimi, Claude Web, etc.) compatible with the OpenAI API format. Control chatbots through a local API server that mimics OpenAI's endpoints, enabling seamless integration with existing tools and applications.

## 🌟 Features

- **🔄 OpenAI API Compatibility**: Drop-in replacement for OpenAI API calls
- **🌐 Universal Chatbot Support**: Works with ChatGPT, Kimi, Claude Web, and custom chatbots
- **🔒 Privacy-First**: All processing happens locally, no data leaves your machine
- **🎭 Detection Resistant**: Human-like interaction patterns to avoid detection
- **⚡ Real-time Streaming**: Supports streaming responses like the original APIs
- **🔧 Easy Configuration**: Simple UI for managing chatbot endpoints and settings
- **🛡️ Robust Error Handling**: Automatic retries, session recovery, and fallback mechanisms

## 🚀 Quick Start

### Prerequisites

- **Chrome/Chromium Browser** (latest version)
- **Python 3.9+**
- **Node.js 16+** (for development)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/universal-chatbot-proxy.git
   cd universal-chatbot-proxy
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install the Chrome extension**
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `extension/` folder

4. **Start the local API server**
   ```bash
   python local_api_proxy/server.py
   ```

5. **Configure your chatbot**
   - Click the extension icon in Chrome
   - Add your preferred chatbot URL (e.g., `https://chat.openai.com`)
   - Configure selectors if using a custom chatbot

## 📖 Usage

### Basic API Call

```python
import openai

# Point to your local proxy instead of OpenAI
openai.api_base = "http://localhost:11435/v1"
openai.api_key = "dummy-key"  # Not used, but required by the library

response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",  # Will be mapped to your configured chatbot
    messages=[
        {"role": "user", "content": "Hello, how are you?"}
    ]
)

print(response.choices[0].message.content)
```

### Streaming Responses

```python
response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Tell me a story"}],
    stream=True
)

for chunk in response:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

### cURL Example

```bash
curl -X POST http://localhost:11435/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Your App     │───▶│  Local API Proxy │───▶│ Chrome Extension│
│                │    │   (FastAPI)      │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │ Chatbot Web UI  │
                                               │ (ChatGPT/Kimi)  │
                                               └─────────────────┘
```

## 🔧 Configuration

### Supported Chatbots

| Chatbot | URL | Status |
|---------|-----|--------|
| ChatGPT Web | `https://chat.openai.com` | ✅ Supported |
| Kimi | `https://kimi.moonshot.cn` | ✅ Supported |
| Claude Web | `https://claude.ai` | 🚧 Coming Soon |
| Custom | User-defined | ✅ Configurable |

### Extension Settings

Access settings by clicking the extension icon:

- **Chatbot URL**: The web interface URL
- **Model Mapping**: Map API model names to chatbot instances
- **Selectors**: Custom DOM selectors for input/output elements
- **Delays**: Configure human-like typing delays
- **Debug Mode**: Enable detailed logging

## 📚 Documentation

- [**Architecture Guide**](docs/ARCHITECTURE.md) - Detailed system design
- [**API Reference**](docs/API_REFERENCE.md) - Complete API documentation
- [**Development Guide**](docs/DEVELOPMENT.md) - Setup for contributors
- [**Deployment Guide**](docs/DEPLOYMENT.md) - Production deployment
- [**Troubleshooting**](docs/TROUBLESHOOTING.md) - Common issues and solutions

## 🧪 Testing

```bash
# Run Python tests
pytest tests/

# Run extension tests
cd extension && npm test

# Run integration tests
python tests/integration/test_e2e.py
```

## 🤝 Contributing

We welcome contributions! Please see our [Development Guide](docs/DEVELOPMENT.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This tool is for educational and personal use only. Please respect the terms of service of the chatbot platforms you interact with. The authors are not responsible for any misuse or violations of third-party terms of service.

## 🙏 Acknowledgments

- OpenAI for the API specification
- Chrome Extensions team for the excellent documentation
- FastAPI for the amazing web framework
- All contributors and users of this project

---

**Made with ❤️ by the Universal Chatbot Proxy team**
