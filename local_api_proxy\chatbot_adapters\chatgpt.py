"""
Universal Chatbot Proxy - ChatGPT Adapter
Adapter for ChatGPT web interface
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any

from .base import BaseChatbotAdapter, ChatbotConfig, ChatbotSelectors, adapter_registry


class ChatGPTAdapter(BaseChatbotAdapter):
    """Adapter for ChatGPT web interface"""
    
    def __init__(self, config: Optional[ChatbotConfig] = None):
        if config is None:
            config = self._get_default_config()
        
        super().__init__(config)
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # ChatGPT-specific state
        self.conversation_id = None
        self.message_count = 0
        self.last_response_time = None
        
        # Response tracking
        self.waiting_for_response = False
        self.current_response = ""
        self.response_complete = False
    
    @classmethod
    def _get_default_config(cls) -> ChatbotConfig:
        """Get default ChatGPT configuration"""
        selectors = ChatbotSelectors(
            input='textarea[data-id="root"]',
            submit='button[data-testid="send-button"]',
            output='[data-message-author-role="assistant"]',
            loading='.result-streaming',
            error='.text-red-500',
            new_chat='nav a[href="/"]',
            clear_chat='button[data-testid="clear-conversation-button"]'
        )
        
        return ChatbotConfig(
            name="chatgpt",
            url="https://chat.openai.com",
            selectors=selectors,
            models=["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o"],
            timeout=60,
            max_retries=3
        )
    
    async def initialize(self) -> bool:
        """Initialize the ChatGPT adapter"""
        try:
            self.logger.info("Initializing ChatGPT adapter")
            
            # Validate selectors
            if not self._validate_selectors():
                return False
            
            # Check if we can access the page
            health_check = await self.check_health()
            if not health_check.get("healthy", False):
                self.last_error = "ChatGPT page not accessible"
                return False
            
            self.is_ready = True
            self.logger.info("ChatGPT adapter initialized successfully")
            return True
            
        except Exception as e:
            self.last_error = f"Initialization failed: {str(e)}"
            self.logger.error(self.last_error)
            return False
    
    async def send_message(self, message: str, options: Optional[Dict] = None) -> Dict[str, Any]:
        """Send a message to ChatGPT"""
        if not self.is_ready:
            return self._format_error_response("Adapter not ready")
        
        options = options or {}
        timeout = options.get("timeout", self.config.timeout)
        
        try:
            self.logger.info(f"Sending message to ChatGPT: {message[:100]}...")
            
            # Reset response state
            self.waiting_for_response = True
            self.current_response = ""
            self.response_complete = False
            
            # Send the message via extension
            send_result = await self._send_message_to_extension(message, options)
            
            if not send_result.get("success", False):
                return self._format_error_response(
                    send_result.get("error", "Failed to send message")
                )
            
            # Wait for response
            response = await self._wait_for_response(timeout)
            
            if response.get("success", False):
                self.message_count += 1
                self.last_response_time = time.time()
            
            return response
            
        except Exception as e:
            self.last_error = str(e)
            self.logger.error(f"Error sending message: {e}")
            return self._format_error_response(str(e))
        finally:
            self.waiting_for_response = False
    
    async def get_response(self, timeout: Optional[int] = None) -> Dict[str, Any]:
        """Get the latest response from ChatGPT"""
        timeout = timeout or self.config.timeout
        
        try:
            if self.waiting_for_response:
                return await self._wait_for_response(timeout)
            else:
                # Get the last response from the page
                return await self._extract_last_response()
                
        except Exception as e:
            self.logger.error(f"Error getting response: {e}")
            return self._format_error_response(str(e))
    
    async def clear_conversation(self) -> bool:
        """Clear the current ChatGPT conversation"""
        try:
            self.logger.info("Clearing ChatGPT conversation")
            
            # Send clear command to extension
            result = await self._send_command_to_extension("clear_conversation")
            
            if result.get("success", False):
                self.conversation_id = None
                self.message_count = 0
                self.current_response = ""
                self.response_complete = False
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error clearing conversation: {e}")
            return False
    
    async def check_health(self) -> Dict[str, Any]:
        """Check ChatGPT adapter health"""
        try:
            # Send health check to extension
            result = await self._send_command_to_extension("health_check")
            
            healthy = result.get("success", False)
            
            return {
                "healthy": healthy,
                "url": self.url,
                "selectors_valid": self._validate_selectors(),
                "message_count": self.message_count,
                "last_response_time": self.last_response_time,
                "conversation_id": self.conversation_id,
                "error": result.get("error") if not healthy else None
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }
    
    async def _send_message_to_extension(self, message: str, options: Dict) -> Dict[str, Any]:
        """Send message to ChatGPT via extension"""
        # This would be implemented to communicate with the extension
        # For now, return a mock response
        
        command = {
            "action": "send_message",
            "chatbot": "chatgpt",
            "message": message,
            "options": {
                "selectors": {
                    "input": self.selectors.input,
                    "submit": self.selectors.submit,
                    "output": self.selectors.output,
                    "loading": self.selectors.loading
                },
                "typing_delay": self.config.typing_delay,
                "action_delay": self.config.action_delay,
                **options
            }
        }
        
        # TODO: Implement actual extension communication
        # This is a placeholder that would be replaced with real WebSocket communication
        
        return {"success": True, "message_sent": True}
    
    async def _send_command_to_extension(self, command: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Send command to extension"""
        # TODO: Implement actual extension communication
        
        command_data = {
            "action": command,
            "chatbot": "chatgpt",
            "params": params or {}
        }
        
        # Placeholder response
        return {"success": True, "command": command}
    
    async def _wait_for_response(self, timeout: int) -> Dict[str, Any]:
        """Wait for response from ChatGPT"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.response_complete:
                return self._format_success_response(
                    self.current_response,
                    {
                        "model": "gpt-3.5-turbo",  # Default model
                        "conversation_id": self.conversation_id,
                        "message_count": self.message_count,
                        "response_time": time.time() - start_time
                    }
                )
            
            # Check for response updates from extension
            await asyncio.sleep(0.5)
        
        return self._format_error_response("Response timeout", "timeout")
    
    async def _extract_last_response(self) -> Dict[str, Any]:
        """Extract the last response from the page"""
        try:
            # Send command to extension to extract last response
            result = await self._send_command_to_extension("extract_response")
            
            if result.get("success", False):
                content = result.get("content", "")
                return self._format_success_response(
                    content,
                    {
                        "model": "gpt-3.5-turbo",
                        "extracted": True,
                        "conversation_id": self.conversation_id
                    }
                )
            
            return self._format_error_response("Failed to extract response")
            
        except Exception as e:
            return self._format_error_response(str(e))
    
    def update_response(self, content: str, complete: bool = False):
        """Update response content (called by extension bridge)"""
        self.current_response = content
        self.response_complete = complete
        
        if complete:
            self.waiting_for_response = False
    
    def set_conversation_id(self, conversation_id: str):
        """Set conversation ID"""
        self.conversation_id = conversation_id
    
    def get_chatgpt_specific_status(self) -> Dict[str, Any]:
        """Get ChatGPT-specific status information"""
        return {
            **self.get_status(),
            "conversation_id": self.conversation_id,
            "message_count": self.message_count,
            "last_response_time": self.last_response_time,
            "waiting_for_response": self.waiting_for_response,
            "response_complete": self.response_complete,
            "current_response_length": len(self.current_response)
        }


# Register the adapter
adapter_registry.register("chatgpt", ChatGPTAdapter)
