{"name": "boxen", "version": "7.1.1", "description": "Create boxes in the terminal", "license": "MIT", "repository": "sindresorhus/boxen", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["cli", "box", "boxes", "terminal", "term", "console", "ascii", "unicode", "border", "text"], "dependencies": {"ansi-align": "^3.0.1", "camelcase": "^7.0.1", "chalk": "^5.2.0", "cli-boxes": "^3.0.0", "string-width": "^5.1.2", "type-fest": "^2.13.0", "widest-line": "^4.0.1", "wrap-ansi": "^8.1.0"}, "devDependencies": {"ava": "^5.2.0", "nyc": "^15.1.0", "tsd": "^0.28.1", "xo": "^0.54.2"}, "ava": {"snapshotDir": "tests/snapshots", "environmentVariables": {"COLUMNS": "60", "FORCE_COLOR": "0"}}, "xo": {"rules": {"@typescript-eslint/no-redundant-type-constituents": "off", "unicorn/prefer-logical-operator-over-ternary": "off", "@typescript-eslint/no-unsafe-assignment": "off"}}}