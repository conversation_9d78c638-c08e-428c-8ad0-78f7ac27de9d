# Include base requirements
-r requirements.txt

# Development tools
pre-commit>=3.5.0
isort>=5.12.0
autopep8>=2.0.4

# Testing and coverage
pytest-mock>=3.12.0
pytest-xdist>=3.5.0
coverage>=7.3.0
factory-boy>=3.3.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.4.0
mkdocs-mermaid2-plugin>=1.1.0

# Debugging and profiling
ipdb>=0.13.13
memory-profiler>=0.61.0
line-profiler>=4.1.0

# Code quality
bandit>=1.7.5  # Security linting
safety>=2.3.0  # Dependency vulnerability scanning
vulture>=2.10  # Dead code detection

# Type checking
types-PyYAML>=6.0.0
types-requests>=2.31.0

# Build and packaging
build>=1.0.0
twine>=4.0.0
wheel>=0.42.0
