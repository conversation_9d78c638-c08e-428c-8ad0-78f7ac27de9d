
# 🤖 Universal Chatbot Proxy - Project Overview

> **⚠️ This documentation has been restructured and improved. Please refer to the individual documentation files for detailed information.**

## 📚 Documentation Structure

This project now includes comprehensive documentation organized as follows:

- **[README.md](README.md)** - Main project overview, quick start, and usage examples
- **[ARCHITECTURE.md](docs/ARCHITECTURE.md)** - Detailed technical architecture and system design
- **[API_REFERENCE.md](docs/API_REFERENCE.md)** - Complete API documentation with examples
- **[DEVELOPMENT.md](docs/DEVELOPMENT.md)** - Development setup and contribution guidelines
- **[DEPLOYMENT.md](docs/DEPLOYMENT.md)** - Installation and deployment instructions
- **[TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)** - Common issues and solutions

## 🚀 Quick Start

For immediate setup, see the [README.md](README.md) file.

For detailed architecture information, see [ARCHITECTURE.md](docs/ARCHITECTURE.md).

## 🏗️ System Overview

The Universal Chatbot Proxy is a sophisticated system that enables any web-based chatbot to be controlled through OpenAI-compatible API calls. It consists of three main components:

### Core Components

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Chrome Extension** | JavaScript (Manifest v3) | Direct interaction with chatbot web interfaces |
| **Local API Server** | Python FastAPI | OpenAI-compatible API endpoints on localhost:11435 |
| **Communication Bridge** | WebSocket + Native Messaging | Real-time communication between components |

### Data Flow

```mermaid
graph LR
    A[Client App] -->|OpenAI API Call| B[Local API Server]
    B -->|WebSocket| C[Chrome Extension]
    C -->|DOM Control| D[Chatbot Web UI]
    D -->|Response| C
    C -->|Parsed Data| B
    B -->|OpenAI Format| A
```

## 🎯 Key Features

- **🔄 OpenAI API Compatibility** - Drop-in replacement for OpenAI API calls
- **🌐 Universal Chatbot Support** - Works with ChatGPT, Kimi, Claude Web, and custom chatbots
- **🔒 Privacy-First** - All processing happens locally, no data leaves your machine
- **🎭 Detection Resistant** - Human-like interaction patterns to avoid detection
- **⚡ Real-time Streaming** - Supports streaming responses like the original APIs
- **🔧 Easy Configuration** - Simple UI for managing chatbot endpoints and settings

## 📂 Project Structure

```
universal-chatbot-proxy/
├── README.md                    # Main project documentation
├── docs/                        # Detailed documentation
│   ├── ARCHITECTURE.md         # System architecture
│   ├── API_REFERENCE.md        # API documentation
│   ├── DEVELOPMENT.md          # Development guide
│   ├── DEPLOYMENT.md           # Deployment guide
│   └── TROUBLESHOOTING.md      # Troubleshooting guide
├── extension/                   # Chrome extension source
├── local_api_proxy/            # Python API server
├── tests/                      # Test suites
├── scripts/                    # Build and utility scripts
└── config/                     # Configuration files
```

## 🛠️ Development Status

This project is currently under active development. The following components are being implemented:

- [x] **Documentation Structure** - Comprehensive documentation created
- [ ] **Chrome Extension** - Manifest v3 extension with content scripts
- [ ] **Python API Server** - FastAPI server with OpenAI compatibility
- [ ] **Communication Bridge** - WebSocket communication layer
- [ ] **Chatbot Adapters** - Support for ChatGPT, Kimi, and custom chatbots
- [ ] **Testing Suite** - Comprehensive test coverage
- [ ] **Deployment Tools** - Installation and packaging scripts

## 🤝 Contributing

We welcome contributions! Please see our [Development Guide](docs/DEVELOPMENT.md) for details on:

- Setting up the development environment
- Code style guidelines
- Testing procedures
- Pull request process

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Important Notes

- This tool is for educational and personal use only
- Please respect the terms of service of chatbot platforms
- The authors are not responsible for any misuse or violations of third-party terms of service
- All data processing happens locally for privacy protection

---

**For detailed information, please refer to the specific documentation files listed above.**
