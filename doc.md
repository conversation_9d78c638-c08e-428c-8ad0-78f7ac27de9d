

---

# **📐 Universal Chatbot Proxy - Architecture**

## **1️⃣ System Overview**

| Layer                         | Purpose                                                     |
| ----------------------------- | ----------------------------------------------------------- |
| **Chrome Extension**          | Controls chatbot UIs directly via content scripts           |
| **Local API Server (Python)** | Accepts OpenAI API calls on `localhost`                     |
| **User Config (Frontend UI)** | Allows users to configure chatbot URL, model aliasing, etc. |

---

## **2️⃣ Data Flow**

### **High-Level Flow**

```
[ Your App / Vibe Tool ]
           │
           │ OpenAI API Call (POST /v1/chat/completions)
           ▼
[ Local API Proxy (Python FastAPI) ]
           │
           │ Sends message to Chrome Extension via Native Messaging / WebSocket / HTTP
           ▼
[ Chrome Extension Background Script ]
           │
           │ Forwards message to Chatbot Web UI via Content Script
           ▼
[ Chatbot Web Page (ChatGPT / Kimi / etc.) ]
           │
           │ DOM Injection: Sends message + Reads response
           ▼
[ Chrome Extension ]
           │
           │ Returns parsed output in OpenAI format
           ▼
[ Local API Proxy → Your App ]
```

---

## **3️⃣ Modular Components**

| Component                        | Technology                          | Role                                                      |
| -------------------------------- | ----------------------------------- | --------------------------------------------------------- |
| **Extension Content Script**     | JavaScript                          | Interact with chatbot UI (DOM manipulation)               |
| **Extension Background Script**  | JavaScript                          | Message routing, URL control, session handling            |
| **Extension UI (Popup/Options)** | HTML/JS                             | Allow user to configure endpoints, model mapping          |
| **Local API Server**             | Python (FastAPI)                    | Accept `localhost:11435/v1` OpenAI calls                  |
| **Communication Bridge**         | WebSocket / Native Messaging / HTTP | Bi-directional messaging between local server & extension |

---

# **📂 Detailed File Structure**

```
universal-chatbot-proxy/
│
├── extension/
│   ├── manifest.json
│   ├── background.js
│   ├── contentScript.js
│   ├── popup.html / popup.js (UI Config)
│   ├── options.html / options.js (Persistent Config)
│
├── local_api_proxy/
│   ├── server.py (FastAPI)
│   ├── openai_compat.py (mimics OpenAI SDK response)
│   ├── bridge_client.py (communicates with extension)
│
├── docs/
│   ├── ARCHITECTURE.md
│   └── REQUIREMENTS.md
```

---

# **🧱 ARCHITECTURE.md**

## **Core Design Goals**

1. **Universal Chatbot Support**

   * User provides any chatbot URL (ChatGPT, Kimi, etc.)
   * Extension handles DOM injection based on prebuilt or user-defined selectors

2. **OpenAI API Compatibility**

   * Mimic `/v1/chat/completions`, `/v1/completions`, `/v1/embeddings`
   * Return outputs in OpenAI JSON schema

3. **Detectionless Operation**

   * Use human-like input simulation
   * No API calls to LLM servers directly, only browser DOM manipulation
   * Use requestIdleCallback & randomized delays to simulate natural usage

4. **Session-Aware**

   * Supports multiple tabs, remembers last chatbot used
   * Reconnects to session if browser closes

---

## **Execution Strategy**

### **Content Script Actions**

* Detect input box & output containers
* Inject user prompt into input
* Submit using keyboard events or DOM clicks
* Wait for full response (observe mutations in DOM)
* Extract text in streaming or complete form

---

### **Background Script Actions**

* Maintain connection with `local_api_proxy`
* Handle error retries (chat UI reload, anti-AFK mechanisms)
* Switch between chatbot URLs (ChatGPT, Kimi, Poe, etc.)

---

### **Local API Server (Python)**

* Accepts standard `openai.ChatCompletion.create()` calls
* Converts inputs to extension protocol
* Returns responses as OpenAI JSON

---

# **📜 REQUIREMENTS.md**

## **Functional Requirements**

| Feature                       | Description                                 |
| ----------------------------- | ------------------------------------------- |
| **API Proxy**                 | Listen on `localhost:11435/v1`              |
| **Universal Chatbot Control** | Support for ChatGPT, Kimi, Claude Web, etc. |
| **Dynamic Targeting**         | User selects chatbot via UI or API param    |
| **DOM Parsing Engine**        | Extract text reliably, handle streaming     |
| **Session Persistence**       | Maintain conversation context per chatbot   |

---

## **Non-Functional Requirements**

| Requirement               | Description                                                          |
| ------------------------- | -------------------------------------------------------------------- |
| **Cross-Platform**        | Windows, macOS, Linux                                                |
| **Detection Resistance**  | Mimic human behavior in DOM                                          |
| **Robust Error Handling** | Auto-retry, refresh page, recover from session drops                 |
| **Low Latency**           | Handle response within reasonable bounds (2-5x normal chatbot delay) |
| **Security**              | No data leaves local machine                                         |

---

## **Technical Constraints**

* **Browsers Supported:** Chrome / Chromium
* **Extensions API:** Manifest v3
* **Communication Protocol:** WebSocket preferred (for real-time streaming)
* **Python Compatibility:** 3.9+
* **Chatbot Support:** Any web chatbot with DOM access, including:

| Chatbot                 | Method                       |
| ----------------------- | ---------------------------- |
| **ChatGPT Web/Desktop** | Direct DOM control           |
| **Kimi.com**            | Direct DOM control           |
| **Poe.com**             | (Optional, similar method)   |
| **Custom Chatbots**     | User-defined selector config |

---

## **Error Scenarios Handled**

| Scenario                | Handling                                      |
| ----------------------- | --------------------------------------------- |
| DOM Elements not found  | Retry with exponential backoff                |
| Chatbot page changed    | Load fallback selectors                       |
| Rate limiting / Captcha | Pause with user notification                  |
| Extension disconnected  | Attempt reconnection                          |
| Browser not open        | Launch new Chrome window via `chrome.runtime` |

---
