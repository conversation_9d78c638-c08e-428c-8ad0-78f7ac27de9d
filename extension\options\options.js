/**
 * Universal Chatbot Proxy - Options Page Script
 * Handles configuration management and settings UI
 */

class OptionsController {
  constructor() {
    this.config = {};
    this.chatbots = {};
    this.currentTab = 'general';
    
    this.init();
  }

  async init() {
    // Setup tab navigation
    this.setupTabNavigation();
    
    // Setup form handlers
    this.setupFormHandlers();
    
    // Load configuration
    await this.loadConfiguration();
    
    // Populate forms
    this.populateForms();
    
    console.log('Options page initialized');
  }

  setupTabNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    navItems.forEach(item => {
      item.addEventListener('click', () => {
        const tabName = item.dataset.tab;
        
        // Update nav items
        navItems.forEach(nav => nav.classList.remove('active'));
        item.classList.add('active');
        
        // Update tab contents
        tabContents.forEach(content => content.classList.remove('active'));
        document.getElementById(tabName).classList.add('active');
        
        this.currentTab = tabName;
      });
    });
  }

  setupFormHandlers() {
    // Save button
    document.getElementById('saveBtn').addEventListener('click', () => this.saveConfiguration());
    
    // Reset button
    document.getElementById('resetBtn').addEventListener('click', () => this.resetConfiguration());
    
    // Test connection button
    document.getElementById('testConnectionBtn').addEventListener('click', () => this.testConnection());
    
    // Add chatbot button
    document.getElementById('addChatbotBtn').addEventListener('click', () => this.showAddChatbotModal());
    
    // Modal handlers
    document.getElementById('modalClose').addEventListener('click', () => this.hideAddChatbotModal());
    document.getElementById('modalCancel').addEventListener('click', () => this.hideAddChatbotModal());
    document.getElementById('modalSave').addEventListener('click', () => this.saveCustomChatbot());
    
    // About section buttons
    document.getElementById('viewDocsBtn').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://github.com/yourusername/universal-chatbot-proxy/docs' });
    });
    
    document.getElementById('reportIssueBtn').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://github.com/yourusername/universal-chatbot-proxy/issues' });
    });
    
    document.getElementById('checkUpdatesBtn').addEventListener('click', () => this.checkForUpdates());
  }

  async loadConfiguration() {
    try {
      const result = await chrome.storage.sync.get(null);
      
      this.config = {
        apiUrl: result.apiUrl || 'http://localhost:11435',
        wsUrl: result.wsUrl || 'ws://localhost:11436',
        defaultChatbot: result.defaultChatbot || 'chatgpt',
        autoConnect: result.autoConnect !== false,
        debugMode: result.debugMode || false,
        requestTimeout: result.requestTimeout || 30,
        retryAttempts: result.retryAttempts || 3,
        typingDelayMin: result.typingDelayMin || 50,
        typingDelayMax: result.typingDelayMax || 150,
        actionDelayMin: result.actionDelayMin || 1000,
        actionDelayMax: result.actionDelayMax || 3000,
        humanLikeTyping: result.humanLikeTyping !== false,
        randomDelays: result.randomDelays !== false,
        clearDataOnClose: result.clearDataOnClose || false,
        encryptStorage: result.encryptStorage || false,
        ...result
      };
      
      this.chatbots = result.chatbots || this.getDefaultChatbots();
      
    } catch (error) {
      console.error('Failed to load configuration:', error);
      this.showStatus('Failed to load configuration', 'error');
    }
  }

  getDefaultChatbots() {
    return {
      chatgpt: {
        name: 'ChatGPT',
        url: 'https://chat.openai.com',
        enabled: true,
        models: ['gpt-3.5-turbo', 'gpt-4'],
        selectors: {
          input: 'textarea[data-id="root"]',
          submit: 'button[data-testid="send-button"]',
          output: '[data-message-author-role="assistant"]'
        }
      },
      kimi: {
        name: 'Kimi',
        url: 'https://kimi.moonshot.cn',
        enabled: true,
        models: ['kimi'],
        selectors: {
          input: '.chat-input textarea',
          submit: '.send-button',
          output: '.message-content'
        }
      },
      claude: {
        name: 'Claude',
        url: 'https://claude.ai',
        enabled: false,
        models: ['claude-3-sonnet', 'claude-3-opus'],
        selectors: {
          input: 'div[contenteditable="true"]',
          submit: 'button[aria-label="Send Message"]',
          output: '.font-claude-message'
        }
      }
    };
  }

  populateForms() {
    // General tab
    document.getElementById('apiUrl').value = this.config.apiUrl;
    document.getElementById('wsUrl').value = this.config.wsUrl;
    document.getElementById('defaultChatbot').value = this.config.defaultChatbot;
    document.getElementById('autoConnect').checked = this.config.autoConnect;
    document.getElementById('debugMode').checked = this.config.debugMode;
    
    // Advanced tab
    document.getElementById('requestTimeout').value = this.config.requestTimeout;
    document.getElementById('retryAttempts').value = this.config.retryAttempts;
    document.getElementById('typingDelayMin').value = this.config.typingDelayMin;
    document.getElementById('typingDelayMax').value = this.config.typingDelayMax;
    document.getElementById('actionDelayMin').value = this.config.actionDelayMin;
    document.getElementById('actionDelayMax').value = this.config.actionDelayMax;
    document.getElementById('humanLikeTyping').checked = this.config.humanLikeTyping;
    document.getElementById('randomDelays').checked = this.config.randomDelays;
    document.getElementById('clearDataOnClose').checked = this.config.clearDataOnClose;
    document.getElementById('encryptStorage').checked = this.config.encryptStorage;
    
    // Populate chatbot list
    this.populateChatbotList();
  }

  populateChatbotList() {
    const chatbotList = document.getElementById('chatbotList');
    chatbotList.innerHTML = '';
    
    Object.entries(this.chatbots).forEach(([key, chatbot]) => {
      const chatbotCard = this.createChatbotCard(key, chatbot);
      chatbotList.appendChild(chatbotCard);
    });
  }

  createChatbotCard(key, chatbot) {
    const card = document.createElement('div');
    card.className = 'chatbot-card';
    card.style.cssText = `
      background: white;
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
    `;
    
    card.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
        <h3 style="margin: 0; font-size: 16px; color: #2c3e50;">${chatbot.name}</h3>
        <label style="display: flex; align-items: center; gap: 8px;">
          <input type="checkbox" ${chatbot.enabled ? 'checked' : ''} 
                 onchange="optionsController.toggleChatbot('${key}', this.checked)">
          <span style="font-size: 12px; color: #5f6368;">Enabled</span>
        </label>
      </div>
      <div style="font-size: 12px; color: #5f6368; margin-bottom: 8px;">
        <strong>URL:</strong> ${chatbot.url}
      </div>
      <div style="font-size: 12px; color: #5f6368; margin-bottom: 8px;">
        <strong>Models:</strong> ${chatbot.models.join(', ')}
      </div>
      <div style="display: flex; gap: 8px;">
        <button class="btn btn-small btn-secondary" onclick="optionsController.editChatbot('${key}')">
          Edit
        </button>
        ${key.startsWith('custom_') ? `
          <button class="btn btn-small btn-secondary" onclick="optionsController.deleteChatbot('${key}')">
            Delete
          </button>
        ` : ''}
      </div>
    `;
    
    return card;
  }

  async saveConfiguration() {
    try {
      // Collect form data
      const formData = {
        apiUrl: document.getElementById('apiUrl').value,
        wsUrl: document.getElementById('wsUrl').value,
        defaultChatbot: document.getElementById('defaultChatbot').value,
        autoConnect: document.getElementById('autoConnect').checked,
        debugMode: document.getElementById('debugMode').checked,
        requestTimeout: parseInt(document.getElementById('requestTimeout').value),
        retryAttempts: parseInt(document.getElementById('retryAttempts').value),
        typingDelayMin: parseInt(document.getElementById('typingDelayMin').value),
        typingDelayMax: parseInt(document.getElementById('typingDelayMax').value),
        actionDelayMin: parseInt(document.getElementById('actionDelayMin').value),
        actionDelayMax: parseInt(document.getElementById('actionDelayMax').value),
        humanLikeTyping: document.getElementById('humanLikeTyping').checked,
        randomDelays: document.getElementById('randomDelays').checked,
        clearDataOnClose: document.getElementById('clearDataOnClose').checked,
        encryptStorage: document.getElementById('encryptStorage').checked,
        chatbots: this.chatbots
      };
      
      // Validate configuration
      this.validateConfiguration(formData);
      
      // Save to storage
      await chrome.storage.sync.set(formData);
      
      // Update local config
      this.config = formData;
      
      // Notify background script
      chrome.runtime.sendMessage({ type: 'config_updated', payload: formData });
      
      this.showStatus('Configuration saved successfully', 'success');
      
    } catch (error) {
      console.error('Failed to save configuration:', error);
      this.showStatus('Failed to save configuration: ' + error.message, 'error');
    }
  }

  validateConfiguration(config) {
    // Validate URLs
    if (!this.isValidUrl(config.apiUrl)) {
      throw new Error('Invalid API URL');
    }
    
    if (!config.wsUrl.startsWith('ws://') && !config.wsUrl.startsWith('wss://')) {
      throw new Error('Invalid WebSocket URL');
    }
    
    // Validate numeric values
    if (config.requestTimeout < 5 || config.requestTimeout > 300) {
      throw new Error('Request timeout must be between 5 and 300 seconds');
    }
    
    if (config.retryAttempts < 1 || config.retryAttempts > 10) {
      throw new Error('Retry attempts must be between 1 and 10');
    }
    
    // Validate delay ranges
    if (config.typingDelayMin >= config.typingDelayMax) {
      throw new Error('Typing delay min must be less than max');
    }
    
    if (config.actionDelayMin >= config.actionDelayMax) {
      throw new Error('Action delay min must be less than max');
    }
  }

  async resetConfiguration() {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      try {
        await chrome.storage.sync.clear();
        this.config = {};
        this.chatbots = this.getDefaultChatbots();
        this.populateForms();
        this.showStatus('Configuration reset to defaults', 'success');
      } catch (error) {
        console.error('Failed to reset configuration:', error);
        this.showStatus('Failed to reset configuration', 'error');
      }
    }
  }

  async testConnection() {
    try {
      const apiUrl = document.getElementById('apiUrl').value;
      
      this.showStatus('Testing connection...', 'info');
      
      const response = await fetch(`${apiUrl}/v1/health`);
      
      if (response.ok) {
        const data = await response.json();
        this.showStatus(`Connection successful! Server version: ${data.version || 'unknown'}`, 'success');
      } else {
        throw new Error(`Server responded with status ${response.status}`);
      }
      
    } catch (error) {
      console.error('Connection test failed:', error);
      this.showStatus('Connection test failed: ' + error.message, 'error');
    }
  }

  showAddChatbotModal() {
    document.getElementById('addChatbotModal').style.display = 'flex';
  }

  hideAddChatbotModal() {
    document.getElementById('addChatbotModal').style.display = 'none';
    
    // Clear form
    document.getElementById('chatbotName').value = '';
    document.getElementById('chatbotUrl').value = '';
    document.getElementById('inputSelector').value = '';
    document.getElementById('submitSelector').value = '';
    document.getElementById('outputSelector').value = '';
  }

  saveCustomChatbot() {
    try {
      const name = document.getElementById('chatbotName').value.trim();
      const url = document.getElementById('chatbotUrl').value.trim();
      const inputSelector = document.getElementById('inputSelector').value.trim();
      const submitSelector = document.getElementById('submitSelector').value.trim();
      const outputSelector = document.getElementById('outputSelector').value.trim();
      
      if (!name || !url || !inputSelector || !submitSelector || !outputSelector) {
        throw new Error('All fields are required');
      }
      
      if (!this.isValidUrl(url)) {
        throw new Error('Invalid URL');
      }
      
      const key = `custom_${Date.now()}`;
      this.chatbots[key] = {
        name,
        url,
        enabled: true,
        models: [name.toLowerCase().replace(/\s+/g, '-')],
        selectors: {
          input: inputSelector,
          submit: submitSelector,
          output: outputSelector
        }
      };
      
      this.populateChatbotList();
      this.hideAddChatbotModal();
      this.showStatus('Custom chatbot added successfully', 'success');
      
    } catch (error) {
      alert('Error: ' + error.message);
    }
  }

  toggleChatbot(key, enabled) {
    if (this.chatbots[key]) {
      this.chatbots[key].enabled = enabled;
    }
  }

  editChatbot(key) {
    // TODO: Implement chatbot editing
    alert('Chatbot editing will be implemented in a future version');
  }

  deleteChatbot(key) {
    if (confirm('Are you sure you want to delete this chatbot?')) {
      delete this.chatbots[key];
      this.populateChatbotList();
      this.showStatus('Chatbot deleted', 'success');
    }
  }

  async checkForUpdates() {
    try {
      this.showStatus('Checking for updates...', 'info');
      
      // TODO: Implement update checking
      setTimeout(() => {
        this.showStatus('You are running the latest version', 'success');
      }, 1000);
      
    } catch (error) {
      this.showStatus('Failed to check for updates', 'error');
    }
  }

  showStatus(message, type = 'info') {
    const statusText = document.getElementById('statusText');
    statusText.textContent = message;
    statusText.className = `status-text ${type}`;
    
    // Clear status after 5 seconds
    setTimeout(() => {
      statusText.textContent = 'Ready';
      statusText.className = 'status-text';
    }, 5000);
  }

  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }
}

// Global reference for inline event handlers
let optionsController;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  optionsController = new OptionsController();
});
