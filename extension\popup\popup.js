/**
 * Universal Chatbot Proxy - Popup Script
 * Handles popup UI interactions and status display
 */

class PopupController {
  constructor() {
    this.elements = {};
    this.status = null;
    this.activityLog = [];
    
    this.init();
  }

  async init() {
    // Get DOM elements
    this.getElements();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Load initial status
    await this.loadStatus();
    
    // Start status polling
    this.startStatusPolling();
    
    console.log('Popup initialized');
  }

  getElements() {
    this.elements = {
      statusDot: document.getElementById('statusDot'),
      statusText: document.getElementById('statusText'),
      apiStatus: document.getElementById('apiStatus'),
      wsStatus: document.getElementById('wsStatus'),
      activeChatbot: document.getElementById('activeChatbot'),
      connectBtn: document.getElementById('connectBtn'),
      testBtn: document.getElementById('testBtn'),
      refreshBtn: document.getElementById('refreshBtn'),
      chatbotSelect: document.getElementById('chatbotSelect'),
      setChatbotBtn: document.getElementById('setChatbotBtn'),
      activityLog: document.getElementById('activityLog'),
      errorSection: document.getElementById('errorSection'),
      errorMessage: document.getElementById('errorMessage'),
      clearErrorBtn: document.getElementById('clearErrorBtn'),
      optionsBtn: document.getElementById('optionsBtn'),
      helpBtn: document.getElementById('helpBtn'),
      aboutBtn: document.getElementById('aboutBtn')
    };
  }

  setupEventListeners() {
    // Action buttons
    this.elements.connectBtn.addEventListener('click', () => this.handleConnect());
    this.elements.testBtn.addEventListener('click', () => this.handleTest());
    this.elements.refreshBtn.addEventListener('click', () => this.handleRefresh());
    
    // Chatbot selection
    this.elements.setChatbotBtn.addEventListener('click', () => this.handleSetChatbot());
    
    // Error handling
    this.elements.clearErrorBtn.addEventListener('click', () => this.clearError());
    
    // Navigation
    this.elements.optionsBtn.addEventListener('click', () => this.openOptions());
    this.elements.helpBtn.addEventListener('click', () => this.openHelp());
    this.elements.aboutBtn.addEventListener('click', () => this.showAbout());
  }

  async loadStatus() {
    try {
      // Get status from background script
      const response = await this.sendMessage('get_status');
      this.updateStatus(response);
    } catch (error) {
      console.error('Failed to load status:', error);
      this.showError('Failed to load status: ' + error.message);
    }
  }

  updateStatus(status) {
    this.status = status;
    
    // Update connection status
    const isConnected = status.connected;
    this.elements.statusDot.className = `status-dot ${isConnected ? 'connected' : ''}`;
    this.elements.statusText.textContent = isConnected ? 'Connected' : 'Disconnected';
    
    // Update API status
    this.elements.apiStatus.textContent = isConnected ? 'Online' : 'Offline';
    this.elements.apiStatus.className = `value ${isConnected ? 'success' : 'error'}`;
    
    // Update WebSocket status
    this.elements.wsStatus.textContent = isConnected ? 'Connected' : 'Disconnected';
    this.elements.wsStatus.className = `value ${isConnected ? 'success' : 'error'}`;
    
    // Update active chatbot
    const activeScript = status.contentScripts?.find(script => script.ready);
    if (activeScript) {
      this.elements.activeChatbot.textContent = activeScript.chatbotType || 'Unknown';
      this.elements.activeChatbot.className = 'value success';
    } else {
      this.elements.activeChatbot.textContent = 'None';
      this.elements.activeChatbot.className = 'value';
    }
    
    // Update button states
    this.elements.connectBtn.textContent = isConnected ? 'Disconnect' : 'Connect';
    this.elements.connectBtn.disabled = false;
    this.elements.testBtn.disabled = !isConnected;
    
    // Update chatbot selector
    if (status.config?.defaultChatbot) {
      this.elements.chatbotSelect.value = status.config.defaultChatbot;
    }
    
    // Add activity log entry
    this.addActivity('Status updated');
  }

  async handleConnect() {
    try {
      this.elements.connectBtn.disabled = true;
      this.elements.connectBtn.textContent = 'Connecting...';
      
      if (this.status?.connected) {
        // Disconnect
        await this.sendMessage('disconnect');
        this.addActivity('Disconnected from API server');
      } else {
        // Connect
        await this.sendMessage('connect');
        this.addActivity('Connected to API server');
      }
      
      // Refresh status
      await this.loadStatus();
      
    } catch (error) {
      console.error('Connection error:', error);
      this.showError('Connection failed: ' + error.message);
      this.elements.connectBtn.disabled = false;
      this.elements.connectBtn.textContent = 'Connect';
    }
  }

  async handleTest() {
    try {
      this.elements.testBtn.disabled = true;
      this.elements.testBtn.textContent = 'Testing...';
      
      const testMessage = {
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello! This is a test message.' }]
      };
      
      const response = await this.sendMessage('test_chat', testMessage);
      
      if (response.success) {
        this.addActivity('Test successful');
        this.showSuccess('Test completed successfully!');
      } else {
        throw new Error(response.error || 'Test failed');
      }
      
    } catch (error) {
      console.error('Test error:', error);
      this.showError('Test failed: ' + error.message);
      this.addActivity('Test failed');
    } finally {
      this.elements.testBtn.disabled = false;
      this.elements.testBtn.textContent = 'Test';
    }
  }

  async handleRefresh() {
    try {
      this.elements.refreshBtn.disabled = true;
      this.elements.refreshBtn.innerHTML = '<span class="btn-icon">⟳</span>Refreshing...';
      
      await this.loadStatus();
      this.addActivity('Status refreshed');
      
    } catch (error) {
      console.error('Refresh error:', error);
      this.showError('Refresh failed: ' + error.message);
    } finally {
      this.elements.refreshBtn.disabled = false;
      this.elements.refreshBtn.innerHTML = '<span class="btn-icon">🔄</span>Refresh';
    }
  }

  async handleSetChatbot() {
    try {
      const selectedChatbot = this.elements.chatbotSelect.value;
      if (!selectedChatbot) {
        this.showError('Please select a chatbot');
        return;
      }
      
      this.elements.setChatbotBtn.disabled = true;
      this.elements.setChatbotBtn.textContent = 'Setting...';
      
      await this.sendMessage('set_default_chatbot', { chatbot: selectedChatbot });
      
      this.addActivity(`Default chatbot set to ${selectedChatbot}`);
      await this.loadStatus();
      
    } catch (error) {
      console.error('Set chatbot error:', error);
      this.showError('Failed to set chatbot: ' + error.message);
    } finally {
      this.elements.setChatbotBtn.disabled = false;
      this.elements.setChatbotBtn.textContent = 'Set Active';
    }
  }

  addActivity(text) {
    const now = new Date();
    const time = now.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    this.activityLog.unshift({ time, text, timestamp: now });
    
    // Keep only last 10 entries
    if (this.activityLog.length > 10) {
      this.activityLog = this.activityLog.slice(0, 10);
    }
    
    this.updateActivityDisplay();
  }

  updateActivityDisplay() {
    this.elements.activityLog.innerHTML = '';
    
    this.activityLog.forEach(entry => {
      const item = document.createElement('div');
      item.className = 'activity-item';
      item.innerHTML = `
        <span class="activity-time">${entry.time}</span>
        <span class="activity-text">${entry.text}</span>
      `;
      this.elements.activityLog.appendChild(item);
    });
  }

  showError(message) {
    this.elements.errorMessage.textContent = message;
    this.elements.errorSection.style.display = 'block';
  }

  clearError() {
    this.elements.errorSection.style.display = 'none';
    this.elements.errorMessage.textContent = '';
  }

  showSuccess(message) {
    // Create temporary success message
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
      background: #e8f5e8;
      border: 1px solid #4caf50;
      color: #2e7d32;
      padding: 8px 12px;
      border-radius: 4px;
      margin: 8px 0;
      font-size: 12px;
    `;
    successDiv.textContent = message;
    
    this.elements.errorSection.parentNode.insertBefore(successDiv, this.elements.errorSection);
    
    setTimeout(() => {
      successDiv.remove();
    }, 3000);
  }

  openOptions() {
    chrome.runtime.openOptionsPage();
  }

  openHelp() {
    chrome.tabs.create({ 
      url: 'https://github.com/yourusername/universal-chatbot-proxy/docs' 
    });
  }

  showAbout() {
    const aboutText = `
Universal Chatbot Proxy v1.0.0

A universal proxy that makes any web-based chatbot compatible with OpenAI API format.

Features:
• OpenAI API compatibility
• Multiple chatbot support
• Privacy-first design
• Real-time streaming

© 2024 Universal Chatbot Proxy Team
Licensed under MIT License
    `.trim();
    
    alert(aboutText);
  }

  async sendMessage(type, payload = {}) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ type, payload }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else if (response && response.error) {
          reject(new Error(response.error));
        } else {
          resolve(response);
        }
      });
    });
  }

  startStatusPolling() {
    // Poll status every 5 seconds
    setInterval(async () => {
      try {
        await this.loadStatus();
      } catch (error) {
        console.error('Status polling error:', error);
      }
    }, 5000);
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
