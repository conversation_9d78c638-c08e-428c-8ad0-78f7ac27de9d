"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.promisify=promisify;var customArgumentsToken="__ES6-PROMISIFY--CUSTOM-ARGUMENTS__";function promisify(a){if("function"!=typeof a)throw new TypeError("Argument to promisify must be a function");var b=a[customArgumentsToken],c=promisify.Promise||Promise;if("function"!=typeof c)throw new Error("No Promise implementation found; do you need a polyfill?");return function(){for(var d=this,e=arguments.length,f=Array(e),g=0;g<e;g++)f[g]=arguments[g];return new c(function(c,e){f.push(function(a){if(a)return e(a);for(var d=arguments.length,f=Array(1<d?d-1:0),g=1;g<d;g++)f[g-1]=arguments[g];if(1===f.length||!b)return c(f[0]);var h={};f.forEach(function(a,c){var d=b[c];d&&(h[d]=a)}),c(h)}),a.apply(d,f)})}}promisify.argumentNames="__ES6-PROMISIFY--CUSTOM-ARGUMENTS__",promisify.Promise=void 0;
