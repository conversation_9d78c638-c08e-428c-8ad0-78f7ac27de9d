/**
 * Universal Chatbot Proxy - Content Script
 * Handles DOM manipulation and chatbot interaction
 */

// Import utilities
const { U<PERSON><PERSON>, Logger, CONFIG, MessageTypes } = window.ChatbotProxyUtils;
const logger = new Logger('ContentScript');

class ChatbotController {
  constructor() {
    this.isInitialized = false;
    this.currentAdapter = null;
    this.messageQueue = [];
    this.isProcessing = false;
    this.observers = [];
    this.config = null;
    
    this.init();
  }

  async init() {
    try {
      logger.info('Initializing content script on:', window.location.href);
      
      // Wait for page to be ready
      if (document.readyState !== 'complete') {
        await new Promise(resolve => {
          window.addEventListener('load', resolve);
        });
      }

      // Detect chatbot type and load appropriate adapter
      await this.detectAndLoadAdapter();
      
      // Setup message listeners
      this.setupMessageListeners();
      
      // Notify background script that content script is ready
      this.sendMessage(MessageTypes.CONTENT_READY, {
        url: window.location.href,
        chatbotType: this.currentAdapter?.type || 'unknown',
        timestamp: Date.now()
      });

      this.isInitialized = true;
      logger.info('Content script initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize content script:', error);
      this.sendMessage(MessageTypes.ERROR_OCCURRED, {
        error: error.message,
        stack: error.stack
      });
    }
  }

  async detectAndLoadAdapter() {
    const url = window.location.href;
    const hostname = window.location.hostname;

    // Detect chatbot type based on URL
    if (hostname.includes('chat.openai.com')) {
      this.currentAdapter = new ChatGPTAdapter();
    } else if (hostname.includes('kimi.moonshot.cn')) {
      this.currentAdapter = new KimiAdapter();
    } else if (hostname.includes('claude.ai')) {
      this.currentAdapter = new ClaudeAdapter();
    } else {
      // Try to load custom adapter configuration
      this.currentAdapter = new GenericAdapter();
    }

    if (this.currentAdapter) {
      await this.currentAdapter.initialize();
      logger.info(`Loaded adapter: ${this.currentAdapter.type}`);
    } else {
      throw new Error(`No adapter found for ${hostname}`);
    }
  }

  setupMessageListeners() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Listen for page navigation
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      logger.debug('Received message:', message.type);

      switch (message.type) {
        case MessageTypes.SEND_MESSAGE:
          await this.handleSendMessage(message.payload, sendResponse);
          break;

        case MessageTypes.GET_STATUS:
          sendResponse(this.getStatus());
          break;

        case MessageTypes.CONFIGURE:
          await this.configure(message.payload);
          sendResponse({ success: true });
          break;

        case MessageTypes.RESET:
          await this.reset();
          sendResponse({ success: true });
          break;

        default:
          logger.warn('Unknown message type:', message.type);
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      logger.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async handleSendMessage(payload, sendResponse) {
    if (!this.currentAdapter) {
      throw new Error('No adapter available');
    }

    if (this.isProcessing) {
      throw new Error('Already processing a message');
    }

    try {
      this.isProcessing = true;
      
      const { messages, options = {} } = payload;
      const lastMessage = messages[messages.length - 1];
      
      if (!lastMessage || lastMessage.role !== 'user') {
        throw new Error('Invalid message format');
      }

      logger.info('Sending message to chatbot:', lastMessage.content.substring(0, 100));

      // Send message to chatbot
      await this.currentAdapter.sendMessage(lastMessage.content, options);

      // Wait for and extract response
      const response = await this.currentAdapter.waitForResponse(options);

      // Send response back
      sendResponse({
        success: true,
        response: {
          role: 'assistant',
          content: response.content,
          timestamp: Date.now()
        },
        metadata: response.metadata || {}
      });

      // Notify background script
      this.sendMessage(MessageTypes.RESPONSE_RECEIVED, {
        content: response.content,
        timestamp: Date.now()
      });

    } finally {
      this.isProcessing = false;
    }
  }

  getStatus() {
    return {
      initialized: this.isInitialized,
      adapterType: this.currentAdapter?.type || null,
      processing: this.isProcessing,
      url: window.location.href,
      ready: this.currentAdapter?.isReady() || false
    };
  }

  async configure(config) {
    this.config = config;
    if (this.currentAdapter) {
      await this.currentAdapter.configure(config);
    }
  }

  async reset() {
    this.isProcessing = false;
    this.messageQueue = [];
    
    if (this.currentAdapter) {
      await this.currentAdapter.reset();
    }
  }

  sendMessage(type, payload) {
    chrome.runtime.sendMessage({
      type,
      payload,
      timestamp: Date.now()
    }).catch(error => {
      logger.error('Failed to send message to background:', error);
    });
  }

  cleanup() {
    logger.info('Cleaning up content script');
    
    // Disconnect observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    // Cleanup adapter
    if (this.currentAdapter) {
      this.currentAdapter.cleanup();
    }
  }
}

// Base adapter class
class BaseAdapter {
  constructor(type) {
    this.type = type;
    this.selectors = {};
    this.config = {};
    this.observers = [];
    this.ready = false;
  }

  async initialize() {
    logger.info(`Initializing ${this.type} adapter`);
    await this.loadSelectors();
    await this.waitForElements();
    this.setupObservers();
    this.ready = true;
  }

  async loadSelectors() {
    // Override in subclasses
  }

  async waitForElements() {
    // Wait for required elements to be available
    const requiredSelectors = ['input', 'submit', 'output'];
    
    for (const selectorType of requiredSelectors) {
      if (this.selectors[selectorType]) {
        try {
          await Utils.waitForElement(this.selectors[selectorType], 10000);
          logger.debug(`Found ${selectorType} element:`, this.selectors[selectorType]);
        } catch (error) {
          logger.warn(`Required element not found: ${selectorType}`, error);
        }
      }
    }
  }

  setupObservers() {
    // Setup mutation observers for response detection
    // Override in subclasses
  }

  async sendMessage(content, options = {}) {
    const inputElement = document.querySelector(this.selectors.input);
    if (!inputElement) {
      throw new Error('Input element not found');
    }

    // Clear existing content and type new message
    await Utils.typeText(inputElement, content, {
      clearFirst: true,
      triggerEvents: true,
      typingDelay: options.typingDelay || CONFIG.TYPING_DELAY
    });

    // Wait a moment before submitting
    await Utils.sleep(Utils.randomDelay(500, 1500));

    // Submit the message
    await this.submitMessage();
  }

  async submitMessage() {
    const submitElement = document.querySelector(this.selectors.submit);
    if (!submitElement) {
      throw new Error('Submit element not found');
    }

    await Utils.clickElement(submitElement);
  }

  async waitForResponse(options = {}) {
    const timeout = options.timeout || CONFIG.RESPONSE_TIMEOUT;
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const checkForResponse = () => {
        if (Date.now() - startTime > timeout) {
          reject(new Error('Response timeout'));
          return;
        }

        const response = this.extractResponse();
        if (response && response.content) {
          resolve(response);
        } else {
          setTimeout(checkForResponse, 500);
        }
      };

      checkForResponse();
    });
  }

  extractResponse() {
    // Override in subclasses
    return { content: '', metadata: {} };
  }

  isReady() {
    return this.ready;
  }

  async configure(config) {
    this.config = { ...this.config, ...config };
  }

  async reset() {
    // Override in subclasses if needed
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// ChatGPT adapter
class ChatGPTAdapter extends BaseAdapter {
  constructor() {
    super('chatgpt');
  }

  async loadSelectors() {
    this.selectors = {
      input: 'textarea[data-id="root"]',
      submit: 'button[data-testid="send-button"]',
      output: '[data-message-author-role="assistant"]',
      loading: '.result-streaming',
      error: '.text-red-500'
    };
  }

  extractResponse() {
    const responseElements = document.querySelectorAll(this.selectors.output);
    if (responseElements.length === 0) return null;

    // Get the last response
    const lastResponse = responseElements[responseElements.length - 1];
    const content = Utils.extractText(lastResponse);

    return {
      content: content.trim(),
      metadata: {
        timestamp: Date.now(),
        elementCount: responseElements.length
      }
    };
  }
}

// Kimi adapter
class KimiAdapter extends BaseAdapter {
  constructor() {
    super('kimi');
  }

  async loadSelectors() {
    this.selectors = {
      input: '.chat-input textarea',
      submit: '.send-button',
      output: '.message-content',
      loading: '.typing-indicator',
      error: '.error-message'
    };
  }

  extractResponse() {
    const responseElements = document.querySelectorAll(this.selectors.output);
    if (responseElements.length === 0) return null;

    // Get the last response
    const lastResponse = responseElements[responseElements.length - 1];
    const content = Utils.extractText(lastResponse);

    return {
      content: content.trim(),
      metadata: {
        timestamp: Date.now(),
        elementCount: responseElements.length
      }
    };
  }
}

// Claude adapter
class ClaudeAdapter extends BaseAdapter {
  constructor() {
    super('claude');
  }

  async loadSelectors() {
    this.selectors = {
      input: 'div[contenteditable="true"]',
      submit: 'button[aria-label="Send Message"]',
      output: '.font-claude-message',
      loading: '.animate-pulse',
      error: '.text-red-600'
    };
  }

  extractResponse() {
    const responseElements = document.querySelectorAll(this.selectors.output);
    if (responseElements.length === 0) return null;

    // Get the last response
    const lastResponse = responseElements[responseElements.length - 1];
    const content = Utils.extractText(lastResponse);

    return {
      content: content.trim(),
      metadata: {
        timestamp: Date.now(),
        elementCount: responseElements.length
      }
    };
  }
}

// Generic adapter for custom chatbots
class GenericAdapter extends BaseAdapter {
  constructor() {
    super('generic');
  }

  async loadSelectors() {
    // Load from configuration or use defaults
    this.selectors = this.config.selectors || {
      input: '#chat-input',
      submit: '#send-button',
      output: '.response-container',
      loading: '.loading-indicator',
      error: '.error-container'
    };
  }

  extractResponse() {
    const responseElements = document.querySelectorAll(this.selectors.output);
    if (responseElements.length === 0) return null;

    // Get the last response
    const lastResponse = responseElements[responseElements.length - 1];
    const content = Utils.extractText(lastResponse);

    return {
      content: content.trim(),
      metadata: {
        timestamp: Date.now(),
        elementCount: responseElements.length
      }
    };
  }
}

// Initialize the chatbot controller
const chatbotController = new ChatbotController();
