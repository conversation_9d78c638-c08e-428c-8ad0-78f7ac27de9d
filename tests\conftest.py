"""
Universal Chatbot Proxy - Test Configuration
Pytest configuration and shared fixtures
"""

import asyncio
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock

from local_api_proxy.config import Config, ChatbotConfig
from local_api_proxy.server import create_app
from local_api_proxy.bridge_client import ExtensionBridge
from local_api_proxy.openai_compat import OpenAICompatibilityLayer


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def test_config():
    """Create a test configuration."""
    return Config(
        server={"host": "127.0.0.1", "port": 11435, "debug": True},
        websocket={"port": 11436},
        security={"api_key_required": False, "cors_origins": ["*"]},
        logging={"level": "DEBUG"},
        chatbots={
            "test_chatbot": ChatbotConfig(
                url="https://test.example.com",
                enabled=True,
                models=["test-model"],
                selectors={
                    "input": "#input",
                    "submit": "#submit",
                    "output": ".output"
                }
            )
        }
    )


@pytest.fixture
def mock_extension_bridge():
    """Create a mock extension bridge."""
    bridge = Mock(spec=ExtensionBridge)
    bridge.is_connected = Mock(return_value=True)
    bridge.send_message = AsyncMock(return_value={
        "success": True,
        "payload": {
            "content": "Test response",
            "finish_reason": "stop"
        }
    })
    bridge.send_streaming_message = AsyncMock()
    return bridge


@pytest.fixture
def mock_openai_compat(test_config):
    """Create a mock OpenAI compatibility layer."""
    return OpenAICompatibilityLayer(test_config)


@pytest.fixture
async def test_app(test_config):
    """Create a test FastAPI application."""
    app = create_app()
    # Override config for testing
    app.dependency_overrides = {}
    return app


@pytest.fixture
def sample_chat_request():
    """Sample chat completion request."""
    return {
        "model": "test-model",
        "messages": [
            {"role": "user", "content": "Hello, world!"}
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }


@pytest.fixture
def sample_completion_request():
    """Sample text completion request."""
    return {
        "model": "test-model",
        "prompt": "Hello, world!",
        "max_tokens": 100,
        "temperature": 0.7
    }


@pytest.fixture
def mock_websocket():
    """Create a mock WebSocket connection."""
    websocket = AsyncMock()
    websocket.send = AsyncMock()
    websocket.recv = AsyncMock()
    websocket.close = AsyncMock()
    websocket.closed = False
    return websocket


@pytest.fixture
def mock_chrome_extension():
    """Create a mock Chrome extension environment."""
    extension = Mock()
    extension.send_message = AsyncMock(return_value={
        "success": True,
        "response": "Test response from extension"
    })
    extension.is_connected = Mock(return_value=True)
    return extension


class AsyncContextManager:
    """Helper for creating async context managers in tests."""
    
    def __init__(self, async_func):
        self.async_func = async_func
    
    async def __aenter__(self):
        return await self.async_func()
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


@pytest.fixture
def async_context_manager():
    """Factory for creating async context managers."""
    return AsyncContextManager


# Test data
TEST_MESSAGES = [
    {"role": "user", "content": "Hello"},
    {"role": "assistant", "content": "Hi there!"},
    {"role": "user", "content": "How are you?"},
    {"role": "assistant", "content": "I'm doing well, thank you!"}
]

TEST_MODELS = [
    "gpt-3.5-turbo",
    "gpt-4",
    "kimi",
    "claude-3-sonnet"
]

TEST_CHATBOT_RESPONSES = [
    "This is a test response from the chatbot.",
    "Here's another response with more content.",
    "A longer response that might be used to test streaming functionality and ensure that the system can handle various response lengths properly."
]


# Utility functions for tests
def create_mock_response(content: str, success: bool = True, **kwargs):
    """Create a mock response object."""
    response = {
        "success": success,
        "content": content,
        **kwargs
    }
    return response


def create_mock_error(error_message: str, error_type: str = "test_error"):
    """Create a mock error response."""
    return {
        "success": False,
        "error": error_message,
        "error_type": error_type
    }


async def wait_for_condition(condition_func, timeout: float = 5.0, interval: float = 0.1):
    """Wait for a condition to become true."""
    start_time = asyncio.get_event_loop().time()
    
    while asyncio.get_event_loop().time() - start_time < timeout:
        if await condition_func() if asyncio.iscoroutinefunction(condition_func) else condition_func():
            return True
        await asyncio.sleep(interval)
    
    return False


# Pytest configuration
def pytest_configure(config):
    """Configure pytest."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "extension: mark test as requiring Chrome extension"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection."""
    for item in items:
        # Add markers based on test location
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        if "test_extension" in item.name:
            item.add_marker(pytest.mark.extension)
        
        # Mark async tests
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
