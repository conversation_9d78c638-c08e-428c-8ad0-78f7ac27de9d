#!/usr/bin/env python3
"""Setup script for Universal Chatbot Proxy."""

from setuptools import setup, find_packages
import os

# Read the README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
def read_requirements(filename):
    """Read requirements from file."""
    with open(filename, "r", encoding="utf-8") as f:
        return [
            line.strip()
            for line in f
            if line.strip() and not line.startswith("#") and not line.startswith("-r")
        ]

# Get version from __init__.py
def get_version():
    """Get version from package."""
    version_file = os.path.join("local_api_proxy", "__init__.py")
    with open(version_file, "r", encoding="utf-8") as f:
        for line in f:
            if line.startswith("__version__"):
                return line.split("=")[1].strip().strip('"').strip("'")
    return "0.1.0"

setup(
    name="universal-chatbot-proxy",
    version=get_version(),
    author="Universal Chatbot Proxy Team",
    author_email="<EMAIL>",
    description="A universal proxy that makes any web-based chatbot compatible with OpenAI API",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/universal-chatbot-proxy",
    project_urls={
        "Bug Tracker": "https://github.com/yourusername/universal-chatbot-proxy/issues",
        "Documentation": "https://github.com/yourusername/universal-chatbot-proxy/docs",
        "Source Code": "https://github.com/yourusername/universal-chatbot-proxy",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Communications :: Chat",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements("requirements.txt"),
    extras_require={
        "dev": read_requirements("requirements-dev.txt"),
        "test": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "universal-chatbot-proxy=local_api_proxy.server:main",
            "chatbot-proxy=local_api_proxy.server:main",
        ],
    },
    include_package_data=True,
    package_data={
        "local_api_proxy": [
            "config/*.yaml",
            "templates/*.html",
            "static/*",
        ],
    },
    zip_safe=False,
    keywords=[
        "chatbot",
        "openai",
        "api",
        "proxy",
        "automation",
        "chrome-extension",
        "fastapi",
        "websocket",
    ],
)
