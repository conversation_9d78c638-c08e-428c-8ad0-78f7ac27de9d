"""
Universal Chatbot Proxy - Metrics Collection
Prometheus-compatible metrics collection
"""

import time
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, Counter
import asyncio


@dataclass
class MetricSample:
    """Individual metric sample"""
    name: str
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)


@dataclass
class Counter:
    """Counter metric"""
    name: str
    help: str
    value: float = 0.0
    labels: Dict[str, str] = field(default_factory=dict)
    
    def inc(self, amount: float = 1.0) -> None:
        """Increment counter"""
        self.value += amount


@dataclass
class Gauge:
    """Gauge metric"""
    name: str
    help: str
    value: float = 0.0
    labels: Dict[str, str] = field(default_factory=dict)
    
    def set(self, value: float) -> None:
        """Set gauge value"""
        self.value = value
    
    def inc(self, amount: float = 1.0) -> None:
        """Increment gauge"""
        self.value += amount
    
    def dec(self, amount: float = 1.0) -> None:
        """Decrement gauge"""
        self.value -= amount


@dataclass
class Histogram:
    """Histogram metric"""
    name: str
    help: str
    buckets: List[float] = field(default_factory=lambda: [0.1, 0.5, 1.0, 2.5, 5.0, 10.0])
    counts: Dict[float, int] = field(default_factory=dict)
    sum: float = 0.0
    count: int = 0
    labels: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.counts:
            self.counts = {bucket: 0 for bucket in self.buckets}
            self.counts[float('inf')] = 0
    
    def observe(self, value: float) -> None:
        """Observe a value"""
        self.sum += value
        self.count += 1
        
        for bucket in self.buckets:
            if value <= bucket:
                self.counts[bucket] += 1
        self.counts[float('inf')] += 1


class MetricsCollector:
    """Metrics collector for Prometheus"""
    
    def __init__(self):
        self.counters: Dict[str, Counter] = {}
        self.gauges: Dict[str, Gauge] = {}
        self.histograms: Dict[str, Histogram] = {}
        self.start_time = time.time()
        
        # Initialize default metrics
        self._init_default_metrics()
    
    def _init_default_metrics(self):
        """Initialize default metrics"""
        # Request counters
        self.counters['requests_total'] = Counter(
            'chatbot_proxy_requests_total',
            'Total number of requests'
        )
        
        self.counters['requests_successful'] = Counter(
            'chatbot_proxy_requests_successful_total',
            'Total number of successful requests'
        )
        
        self.counters['requests_failed'] = Counter(
            'chatbot_proxy_requests_failed_total',
            'Total number of failed requests'
        )
        
        # Connection gauges
        self.gauges['active_connections'] = Gauge(
            'chatbot_proxy_active_connections',
            'Number of active connections'
        )
        
        self.gauges['extension_connected'] = Gauge(
            'chatbot_proxy_extension_connected',
            'Extension connection status (1=connected, 0=disconnected)'
        )
        
        # Response time histogram
        self.histograms['request_duration'] = Histogram(
            'chatbot_proxy_request_duration_seconds',
            'Request duration in seconds',
            buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0]
        )
    
    async def start(self):
        """Start metrics collection"""
        pass
    
    async def stop(self):
        """Stop metrics collection"""
        pass
    
    def record_request(self, endpoint: str, model: str = "unknown"):
        """Record a request"""
        self.counters['requests_total'].inc()
    
    def record_success(self, endpoint: str, model: str = "unknown", duration: float = 0.0):
        """Record a successful request"""
        self.counters['requests_successful'].inc()
        if duration > 0:
            self.histograms['request_duration'].observe(duration)
    
    def record_error(self, endpoint: str, error: str = "unknown"):
        """Record a failed request"""
        self.counters['requests_failed'].inc()
    
    def set_active_connections(self, count: int):
        """Set number of active connections"""
        self.gauges['active_connections'].set(count)
    
    def set_extension_connected(self, connected: bool):
        """Set extension connection status"""
        self.gauges['extension_connected'].set(1.0 if connected else 0.0)
    
    def get_prometheus_metrics(self) -> str:
        """Get metrics in Prometheus format"""
        lines = []
        
        # Add uptime
        uptime = time.time() - self.start_time
        lines.append(f"# HELP chatbot_proxy_uptime_seconds Uptime in seconds")
        lines.append(f"# TYPE chatbot_proxy_uptime_seconds gauge")
        lines.append(f"chatbot_proxy_uptime_seconds {uptime}")
        lines.append("")
        
        # Counters
        for counter in self.counters.values():
            lines.append(f"# HELP {counter.name} {counter.help}")
            lines.append(f"# TYPE {counter.name} counter")
            labels_str = self._format_labels(counter.labels)
            lines.append(f"{counter.name}{labels_str} {counter.value}")
            lines.append("")
        
        # Gauges
        for gauge in self.gauges.values():
            lines.append(f"# HELP {gauge.name} {gauge.help}")
            lines.append(f"# TYPE {gauge.name} gauge")
            labels_str = self._format_labels(gauge.labels)
            lines.append(f"{gauge.name}{labels_str} {gauge.value}")
            lines.append("")
        
        # Histograms
        for histogram in self.histograms.values():
            lines.append(f"# HELP {histogram.name} {histogram.help}")
            lines.append(f"# TYPE {histogram.name} histogram")
            
            labels_str = self._format_labels(histogram.labels)
            
            # Buckets
            for bucket, count in histogram.counts.items():
                bucket_str = "+Inf" if bucket == float('inf') else str(bucket)
                bucket_labels = {**histogram.labels, 'le': bucket_str}
                bucket_labels_str = self._format_labels(bucket_labels)
                lines.append(f"{histogram.name}_bucket{bucket_labels_str} {count}")
            
            # Sum and count
            lines.append(f"{histogram.name}_sum{labels_str} {histogram.sum}")
            lines.append(f"{histogram.name}_count{labels_str} {histogram.count}")
            lines.append("")
        
        return "\n".join(lines)
    
    def _format_labels(self, labels: Dict[str, str]) -> str:
        """Format labels for Prometheus"""
        if not labels:
            return ""
        
        label_pairs = [f'{key}="{value}"' for key, value in labels.items()]
        return "{" + ",".join(label_pairs) + "}"
    
    def get_stats(self) -> Dict:
        """Get current statistics"""
        return {
            "uptime": time.time() - self.start_time,
            "total_requests": self.counters['requests_total'].value,
            "successful_requests": self.counters['requests_successful'].value,
            "failed_requests": self.counters['requests_failed'].value,
            "active_connections": self.gauges['active_connections'].value,
            "extension_connected": bool(self.gauges['extension_connected'].value),
            "average_response_time": (
                self.histograms['request_duration'].sum / 
                max(1, self.histograms['request_duration'].count)
            )
        }


class RequestTimer:
    """Context manager for timing requests"""
    
    def __init__(self, metrics: MetricsCollector, endpoint: str, model: str = "unknown"):
        self.metrics = metrics
        self.endpoint = endpoint
        self.model = model
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.metrics.record_request(self.endpoint, self.model)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.metrics.record_success(self.endpoint, self.model, duration)
        else:
            self.metrics.record_error(self.endpoint, str(exc_val) if exc_val else "unknown")


# Global metrics instance
_metrics: Optional[MetricsCollector] = None


def get_metrics() -> Optional[MetricsCollector]:
    """Get global metrics instance"""
    return _metrics


def set_metrics(metrics: MetricsCollector) -> None:
    """Set global metrics instance"""
    global _metrics
    _metrics = metrics
