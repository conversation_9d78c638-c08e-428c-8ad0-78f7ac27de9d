var customArgumentsToken="__ES6-PROMISIFY--CUSTOM-ARGUMENTS__";export function promisify(a){if("function"!=typeof a)throw new TypeError("Argument to promisify must be a function");var b=a[customArgumentsToken],c=promisify.Promise||Promise;if("function"!=typeof c)throw new Error("No Promise implementation found; do you need a polyfill?");return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return new c((c,d)=>{e.push(function(a){if(a)return d(a);for(var e=arguments.length,f=Array(1<e?e-1:0),g=1;g<e;g++)f[g-1]=arguments[g];if(1===f.length||!b)return c(f[0]);var h={};f.forEach((a,c)=>{var d=b[c];d&&(h[d]=a)}),c(h)}),a.apply(this,e)})}}promisify.argumentNames="__ES6-PROMISIFY--CUSTOM-ARGUMENTS__",promisify.Promise=void 0;
