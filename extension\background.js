/**
 * Universal Chatbot Proxy - Background Script (Service Worker)
 * Handles message routing, session management, and API communication
 */

// Configuration
const CONFIG = {
  API_BASE_URL: 'http://localhost:11435',
  WEBSOCKET_URL: 'ws://localhost:11436',
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  HEARTBEAT_INTERVAL: 30000,
  CONNECTION_TIMEOUT: 10000,
};

// Message types
const MessageTypes = {
  CONTENT_READY: 'content_ready',
  RESPONSE_RECEIVED: 'response_received',
  ERROR_OCCURRED: 'error_occurred',
  STATUS_UPDATE: 'status_update',
  SEND_MESSAGE: 'send_message',
  GET_STATUS: 'get_status',
  CONFIGURE: 'configure',
  RESET: 'reset',
  WS_CONNECT: 'ws_connect',
  WS_DISCONNECT: 'ws_disconnect',
  WS_MESSAGE: 'ws_message',
  WS_ERROR: 'ws_error'
};

class BackgroundService {
  constructor() {
    this.websocket = null;
    this.isConnected = false;
    this.contentScripts = new Map(); // tabId -> script info
    this.pendingRequests = new Map(); // requestId -> request info
    this.config = {};
    this.heartbeatInterval = null;
    
    this.init();
  }

  async init() {
    console.log('Initializing background service...');
    
    // Load configuration
    await this.loadConfig();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Connect to API server
    await this.connectToAPI();
    
    console.log('Background service initialized');
  }

  async loadConfig() {
    try {
      const result = await chrome.storage.sync.get([
        'apiUrl', 'wsUrl', 'defaultChatbot', 'autoConnect', 'debugMode'
      ]);
      
      this.config = {
        apiUrl: result.apiUrl || CONFIG.API_BASE_URL,
        wsUrl: result.wsUrl || CONFIG.WEBSOCKET_URL,
        defaultChatbot: result.defaultChatbot || 'chatgpt',
        autoConnect: result.autoConnect !== false,
        debugMode: result.debugMode || false,
        ...result
      };
      
      console.log('Configuration loaded:', this.config);
    } catch (error) {
      console.error('Failed to load configuration:', error);
      this.config = {
        apiUrl: CONFIG.API_BASE_URL,
        wsUrl: CONFIG.WEBSOCKET_URL,
        defaultChatbot: 'chatgpt',
        autoConnect: true,
        debugMode: false
      };
    }
  }

  setupEventListeners() {
    // Handle messages from content scripts
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open
    });

    // Handle tab updates
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && this.isSupportedUrl(tab.url)) {
        this.registerContentScript(tabId, tab);
      }
    });

    // Handle tab removal
    chrome.tabs.onRemoved.addListener((tabId) => {
      this.contentScripts.delete(tabId);
    });

    // Handle extension startup
    chrome.runtime.onStartup.addListener(() => {
      if (this.config.autoConnect) {
        this.connectToAPI();
      }
    });

    // Handle extension install/update
    chrome.runtime.onInstalled.addListener((details) => {
      console.log('Extension installed/updated:', details.reason);
      if (details.reason === 'install') {
        chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
      }
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      const { type, payload } = message;
      
      switch (type) {
        case MessageTypes.CONTENT_READY:
          await this.handleContentReady(sender.tab.id, payload);
          sendResponse({ success: true });
          break;

        case MessageTypes.RESPONSE_RECEIVED:
          await this.handleResponseReceived(sender.tab.id, payload);
          sendResponse({ success: true });
          break;

        case MessageTypes.ERROR_OCCURRED:
          await this.handleError(sender.tab.id, payload);
          sendResponse({ success: true });
          break;

        case MessageTypes.STATUS_UPDATE:
          await this.handleStatusUpdate(sender.tab.id, payload);
          sendResponse({ success: true });
          break;

        default:
          console.warn('Unknown message type:', type);
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async handleContentReady(tabId, payload) {
    console.log(`Content script ready on tab ${tabId}:`, payload);
    
    this.contentScripts.set(tabId, {
      url: payload.url,
      chatbotType: payload.chatbotType,
      ready: true,
      lastActivity: Date.now()
    });

    // Send configuration to content script
    await this.sendToContentScript(tabId, MessageTypes.CONFIGURE, this.config);
  }

  async handleResponseReceived(tabId, payload) {
    console.log(`Response received from tab ${tabId}`);
    
    // Update activity timestamp
    const scriptInfo = this.contentScripts.get(tabId);
    if (scriptInfo) {
      scriptInfo.lastActivity = Date.now();
    }

    // Forward to API server if needed
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'response',
        tabId,
        payload
      }));
    }
  }

  async handleError(tabId, payload) {
    console.error(`Error from tab ${tabId}:`, payload);
    
    // Update script status
    const scriptInfo = this.contentScripts.get(tabId);
    if (scriptInfo) {
      scriptInfo.lastError = payload;
      scriptInfo.lastActivity = Date.now();
    }
  }

  async handleStatusUpdate(tabId, payload) {
    const scriptInfo = this.contentScripts.get(tabId);
    if (scriptInfo) {
      Object.assign(scriptInfo, payload);
      scriptInfo.lastActivity = Date.now();
    }
  }

  async connectToAPI() {
    try {
      console.log('Connecting to API server...');
      
      // Test HTTP connection first
      const response = await fetch(`${this.config.apiUrl}/v1/health`);
      if (!response.ok) {
        throw new Error(`API server not responding: ${response.status}`);
      }

      // Connect WebSocket
      await this.connectWebSocket();
      
      this.isConnected = true;
      console.log('Connected to API server');
      
    } catch (error) {
      console.error('Failed to connect to API server:', error);
      this.isConnected = false;
      
      // Retry connection
      setTimeout(() => this.connectToAPI(), CONFIG.RETRY_DELAY);
    }
  }

  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(this.config.wsUrl);
        
        this.websocket.onopen = () => {
          console.log('WebSocket connected');
          this.startHeartbeat();
          resolve();
        };

        this.websocket.onmessage = (event) => {
          this.handleWebSocketMessage(event);
        };

        this.websocket.onclose = () => {
          console.log('WebSocket disconnected');
          this.isConnected = false;
          this.stopHeartbeat();
          
          // Attempt reconnection
          setTimeout(() => this.connectWebSocket(), CONFIG.RETRY_DELAY);
        };

        this.websocket.onerror = (error) => {
          console.error('WebSocket error:', error);
          reject(error);
        };

        // Connection timeout
        setTimeout(() => {
          if (this.websocket.readyState !== WebSocket.OPEN) {
            this.websocket.close();
            reject(new Error('WebSocket connection timeout'));
          }
        }, CONFIG.CONNECTION_TIMEOUT);

      } catch (error) {
        reject(error);
      }
    });
  }

  async handleWebSocketMessage(event) {
    try {
      const message = JSON.parse(event.data);
      console.log('WebSocket message received:', message.type);

      switch (message.type) {
        case 'chat_request':
          await this.handleChatRequest(message);
          break;

        case 'ping':
          this.sendWebSocketMessage({ type: 'pong', timestamp: Date.now() });
          break;

        case 'config_update':
          await this.updateConfig(message.payload);
          break;

        default:
          console.warn('Unknown WebSocket message type:', message.type);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }

  async handleChatRequest(message) {
    const { id, payload } = message;
    
    try {
      // Find appropriate content script
      const tabId = await this.findBestTab(payload.chatbot || this.config.defaultChatbot);
      
      if (!tabId) {
        throw new Error('No suitable chatbot tab found');
      }

      // Send message to content script
      const response = await this.sendToContentScript(tabId, MessageTypes.SEND_MESSAGE, payload);
      
      // Send response back via WebSocket
      this.sendWebSocketMessage({
        type: 'chat_response',
        id,
        success: true,
        payload: response
      });

    } catch (error) {
      console.error('Error handling chat request:', error);
      
      this.sendWebSocketMessage({
        type: 'chat_response',
        id,
        success: false,
        error: error.message
      });
    }
  }

  async findBestTab(chatbotType) {
    // Find the best tab for the requested chatbot type
    for (const [tabId, scriptInfo] of this.contentScripts.entries()) {
      if (scriptInfo.ready && scriptInfo.chatbotType === chatbotType) {
        // Verify tab still exists
        try {
          await chrome.tabs.get(tabId);
          return tabId;
        } catch {
          this.contentScripts.delete(tabId);
        }
      }
    }

    // If no exact match, try to open a new tab
    return await this.openChatbotTab(chatbotType);
  }

  async openChatbotTab(chatbotType) {
    const urls = {
      chatgpt: 'https://chat.openai.com',
      kimi: 'https://kimi.moonshot.cn',
      claude: 'https://claude.ai'
    };

    const url = urls[chatbotType];
    if (!url) {
      throw new Error(`Unknown chatbot type: ${chatbotType}`);
    }

    try {
      const tab = await chrome.tabs.create({ url, active: false });
      
      // Wait for content script to be ready
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout waiting for content script'));
        }, 30000);

        const checkReady = () => {
          const scriptInfo = this.contentScripts.get(tab.id);
          if (scriptInfo && scriptInfo.ready) {
            clearTimeout(timeout);
            resolve(tab.id);
          } else {
            setTimeout(checkReady, 1000);
          }
        };

        checkReady();
      });
    } catch (error) {
      throw new Error(`Failed to open chatbot tab: ${error.message}`);
    }
  }

  async sendToContentScript(tabId, type, payload) {
    return new Promise((resolve, reject) => {
      chrome.tabs.sendMessage(tabId, { type, payload }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else if (response && response.error) {
          reject(new Error(response.error));
        } else {
          resolve(response);
        }
      });
    });
  }

  sendWebSocketMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    }
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      this.sendWebSocketMessage({ type: 'ping', timestamp: Date.now() });
    }, CONFIG.HEARTBEAT_INTERVAL);
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  isSupportedUrl(url) {
    if (!url) return false;
    
    const supportedDomains = [
      'chat.openai.com',
      'kimi.moonshot.cn',
      'claude.ai'
    ];

    return supportedDomains.some(domain => url.includes(domain));
  }

  registerContentScript(tabId, tab) {
    console.log(`Registering content script for tab ${tabId}: ${tab.url}`);
    
    this.contentScripts.set(tabId, {
      url: tab.url,
      chatbotType: this.detectChatbotType(tab.url),
      ready: false,
      lastActivity: Date.now()
    });
  }

  detectChatbotType(url) {
    if (url.includes('chat.openai.com')) return 'chatgpt';
    if (url.includes('kimi.moonshot.cn')) return 'kimi';
    if (url.includes('claude.ai')) return 'claude';
    return 'unknown';
  }

  async updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // Save to storage
    await chrome.storage.sync.set(this.config);
    
    // Notify all content scripts
    for (const tabId of this.contentScripts.keys()) {
      try {
        await this.sendToContentScript(tabId, MessageTypes.CONFIGURE, this.config);
      } catch (error) {
        console.error(`Failed to update config for tab ${tabId}:`, error);
      }
    }
  }

  getStatus() {
    return {
      connected: this.isConnected,
      contentScripts: Array.from(this.contentScripts.entries()).map(([tabId, info]) => ({
        tabId,
        ...info
      })),
      config: this.config,
      timestamp: Date.now()
    };
  }
}

// Initialize the background service
const backgroundService = new BackgroundService();

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BackgroundService, MessageTypes, CONFIG };
}
