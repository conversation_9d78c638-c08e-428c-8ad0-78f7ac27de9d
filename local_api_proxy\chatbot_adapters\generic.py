"""
Universal Chatbot Proxy - Generic Adapter
Configurable adapter for custom chatbot interfaces
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any

from .base import BaseChatbot<PERSON>dapter, ChatbotConfig, ChatbotSelectors, adapter_registry


class GenericAdapter(BaseChatbotAdapter):
    """Generic adapter for custom chatbot interfaces"""
    
    def __init__(self, config: ChatbotConfig):
        super().__init__(config)
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # Generic state
        self.session_id = None
        self.message_count = 0
        self.last_response_time = None
        
        # Response tracking
        self.waiting_for_response = False
        self.current_response = ""
        self.response_complete = False
        
        # Custom features
        self.custom_features = config.__dict__.get("custom_features", {})
        self.validation_rules = config.__dict__.get("validation_rules", {})
    
    async def initialize(self) -> bool:
        """Initialize the generic adapter"""
        try:
            self.logger.info(f"Initializing generic adapter for {self.name}")
            
            # Validate selectors
            if not self._validate_selectors():
                return False
            
            # Validate custom configuration
            if not self._validate_custom_config():
                return False
            
            # Check if we can access the page
            health_check = await self.check_health()
            if not health_check.get("healthy", False):
                self.last_error = f"{self.name} page not accessible"
                return False
            
            self.is_ready = True
            self.logger.info(f"Generic adapter for {self.name} initialized successfully")
            return True
            
        except Exception as e:
            self.last_error = f"Initialization failed: {str(e)}"
            self.logger.error(self.last_error)
            return False
    
    async def send_message(self, message: str, options: Optional[Dict] = None) -> Dict[str, Any]:
        """Send a message to the custom chatbot"""
        if not self.is_ready:
            return self._format_error_response("Adapter not ready")
        
        options = options or {}
        timeout = options.get("timeout", self.config.timeout)
        
        try:
            self.logger.info(f"Sending message to {self.name}: {message[:100]}...")
            
            # Validate message if rules are defined
            validation_result = self._validate_message(message)
            if not validation_result.get("valid", True):
                return self._format_error_response(
                    validation_result.get("error", "Message validation failed")
                )
            
            # Reset response state
            self.waiting_for_response = True
            self.current_response = ""
            self.response_complete = False
            
            # Apply custom preprocessing
            processed_message = self._preprocess_message(message, options)
            
            # Send the message via extension
            send_result = await self._send_message_to_extension(processed_message, options)
            
            if not send_result.get("success", False):
                return self._format_error_response(
                    send_result.get("error", "Failed to send message")
                )
            
            # Wait for response
            response = await self._wait_for_response(timeout)
            
            if response.get("success", False):
                self.message_count += 1
                self.last_response_time = time.time()
                
                # Apply custom postprocessing
                response = self._postprocess_response(response)
            
            return response
            
        except Exception as e:
            self.last_error = str(e)
            self.logger.error(f"Error sending message: {e}")
            return self._format_error_response(str(e))
        finally:
            self.waiting_for_response = False
    
    async def get_response(self, timeout: Optional[int] = None) -> Dict[str, Any]:
        """Get the latest response from the custom chatbot"""
        timeout = timeout or self.config.timeout
        
        try:
            if self.waiting_for_response:
                return await self._wait_for_response(timeout)
            else:
                # Get the last response from the page
                return await self._extract_last_response()
                
        except Exception as e:
            self.logger.error(f"Error getting response: {e}")
            return self._format_error_response(str(e))
    
    async def clear_conversation(self) -> bool:
        """Clear the current conversation"""
        try:
            self.logger.info(f"Clearing {self.name} conversation")
            
            # Send clear command to extension
            result = await self._send_command_to_extension("clear_conversation")
            
            if result.get("success", False):
                self.session_id = None
                self.message_count = 0
                self.current_response = ""
                self.response_complete = False
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error clearing conversation: {e}")
            return False
    
    async def check_health(self) -> Dict[str, Any]:
        """Check generic adapter health"""
        try:
            # Send health check to extension
            result = await self._send_command_to_extension("health_check")
            
            healthy = result.get("success", False)
            
            return {
                "healthy": healthy,
                "url": self.url,
                "selectors_valid": self._validate_selectors(),
                "custom_config_valid": self._validate_custom_config(),
                "message_count": self.message_count,
                "last_response_time": self.last_response_time,
                "session_id": self.session_id,
                "custom_features": list(self.custom_features.keys()),
                "error": result.get("error") if not healthy else None
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }
    
    def _validate_custom_config(self) -> bool:
        """Validate custom configuration"""
        try:
            # Check if required custom fields are present
            required_fields = self.validation_rules.get("required_fields", [])
            for field in required_fields:
                if field not in self.custom_features:
                    self.last_error = f"Missing required custom field: {field}"
                    return False
            
            # Validate URL format if specified
            url_pattern = self.validation_rules.get("url_pattern")
            if url_pattern and not self._validate_url_pattern(self.url, url_pattern):
                self.last_error = f"URL does not match required pattern: {url_pattern}"
                return False
            
            return True
            
        except Exception as e:
            self.last_error = f"Custom config validation failed: {str(e)}"
            return False
    
    def _validate_message(self, message: str) -> Dict[str, Any]:
        """Validate message against custom rules"""
        try:
            # Check message length
            max_length = self.validation_rules.get("max_message_length")
            if max_length and len(message) > max_length:
                return {
                    "valid": False,
                    "error": f"Message too long (max {max_length} characters)"
                }
            
            # Check forbidden words
            forbidden_words = self.validation_rules.get("forbidden_words", [])
            for word in forbidden_words:
                if word.lower() in message.lower():
                    return {
                        "valid": False,
                        "error": f"Message contains forbidden word: {word}"
                    }
            
            # Check required format
            required_format = self.validation_rules.get("message_format")
            if required_format and not self._validate_message_format(message, required_format):
                return {
                    "valid": False,
                    "error": f"Message does not match required format: {required_format}"
                }
            
            return {"valid": True}
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Message validation error: {str(e)}"
            }
    
    def _preprocess_message(self, message: str, options: Dict) -> str:
        """Apply custom preprocessing to message"""
        try:
            # Apply custom transformations
            transformations = self.custom_features.get("message_transformations", [])
            
            for transformation in transformations:
                if transformation == "uppercase":
                    message = message.upper()
                elif transformation == "lowercase":
                    message = message.lower()
                elif transformation == "add_prefix":
                    prefix = self.custom_features.get("message_prefix", "")
                    message = f"{prefix}{message}"
                elif transformation == "add_suffix":
                    suffix = self.custom_features.get("message_suffix", "")
                    message = f"{message}{suffix}"
            
            return message
            
        except Exception as e:
            self.logger.warning(f"Message preprocessing failed: {e}")
            return message
    
    def _postprocess_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Apply custom postprocessing to response"""
        try:
            if not response.get("success", False):
                return response
            
            content = response.get("content", "")
            
            # Apply custom response transformations
            transformations = self.custom_features.get("response_transformations", [])
            
            for transformation in transformations:
                if transformation == "remove_html":
                    import re
                    content = re.sub(r'<[^>]+>', '', content)
                elif transformation == "trim_whitespace":
                    content = content.strip()
                elif transformation == "remove_urls":
                    import re
                    content = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', content)
            
            response["content"] = content
            return response
            
        except Exception as e:
            self.logger.warning(f"Response postprocessing failed: {e}")
            return response
    
    def _validate_url_pattern(self, url: str, pattern: str) -> bool:
        """Validate URL against pattern"""
        import re
        try:
            return bool(re.match(pattern, url))
        except re.error:
            return False
    
    def _validate_message_format(self, message: str, format_pattern: str) -> bool:
        """Validate message format"""
        import re
        try:
            return bool(re.match(format_pattern, message))
        except re.error:
            return False
    
    async def _send_message_to_extension(self, message: str, options: Dict) -> Dict[str, Any]:
        """Send message to custom chatbot via extension"""
        command = {
            "action": "send_message",
            "chatbot": "generic",
            "chatbot_name": self.name,
            "message": message,
            "options": {
                "selectors": {
                    "input": self.selectors.input,
                    "submit": self.selectors.submit,
                    "output": self.selectors.output,
                    "loading": self.selectors.loading,
                    "error": self.selectors.error
                },
                "typing_delay": self.config.typing_delay,
                "action_delay": self.config.action_delay,
                "custom_features": self.custom_features,
                **options
            }
        }
        
        # TODO: Implement actual extension communication
        return {"success": True, "message_sent": True}
    
    async def _send_command_to_extension(self, command: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Send command to extension"""
        command_data = {
            "action": command,
            "chatbot": "generic",
            "chatbot_name": self.name,
            "params": params or {}
        }
        
        # TODO: Implement actual extension communication
        return {"success": True, "command": command}
    
    async def _wait_for_response(self, timeout: int) -> Dict[str, Any]:
        """Wait for response from custom chatbot"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.response_complete:
                return self._format_success_response(
                    self.current_response,
                    {
                        "model": self.models[0] if self.models else "custom",
                        "session_id": self.session_id,
                        "message_count": self.message_count,
                        "response_time": time.time() - start_time,
                        "custom_adapter": True,
                        "chatbot_name": self.name
                    }
                )
            
            # Check for response updates from extension
            await asyncio.sleep(0.5)
        
        return self._format_error_response("Response timeout", "timeout")
    
    async def _extract_last_response(self) -> Dict[str, Any]:
        """Extract the last response from the page"""
        try:
            # Send command to extension to extract last response
            result = await self._send_command_to_extension("extract_response")
            
            if result.get("success", False):
                content = result.get("content", "")
                return self._format_success_response(
                    content,
                    {
                        "model": self.models[0] if self.models else "custom",
                        "extracted": True,
                        "session_id": self.session_id,
                        "custom_adapter": True,
                        "chatbot_name": self.name
                    }
                )
            
            return self._format_error_response("Failed to extract response")
            
        except Exception as e:
            return self._format_error_response(str(e))
    
    def update_response(self, content: str, complete: bool = False):
        """Update response content (called by extension bridge)"""
        self.current_response = content
        self.response_complete = complete
        
        if complete:
            self.waiting_for_response = False
    
    def set_session_id(self, session_id: str):
        """Set session ID"""
        self.session_id = session_id
    
    def get_generic_specific_status(self) -> Dict[str, Any]:
        """Get generic adapter specific status information"""
        return {
            **self.get_status(),
            "session_id": self.session_id,
            "message_count": self.message_count,
            "last_response_time": self.last_response_time,
            "waiting_for_response": self.waiting_for_response,
            "response_complete": self.response_complete,
            "current_response_length": len(self.current_response),
            "custom_features": self.custom_features,
            "validation_rules": self.validation_rules
        }


# Register the adapter
adapter_registry.register("generic", GenericAdapter)
