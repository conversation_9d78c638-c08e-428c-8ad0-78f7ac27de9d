# Universal Chatbot Proxy Environment Configuration
# Copy this file to .env and modify the values as needed

# Server Configuration
DEBUG=false
LOG_LEVEL=INFO
API_HOST=127.0.0.1
API_PORT=11435
WEBSOCKET_PORT=11436

# Security Settings
API_KEY_REQUIRED=false
CORS_ORIGINS=["http://localhost:3000"]
RATE_LIMIT_PER_MINUTE=60

# Extension Settings
EXTENSION_ID=your-extension-id-here
DEVELOPMENT_MODE=false

# Chatbot Configuration
DEFAULT_CHATBOT=chatgpt
DEFAULT_MODEL=gpt-3.5-turbo
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# Performance Settings
MAX_CONCURRENT_REQUESTS=10
CONNECTION_POOL_SIZE=50
KEEPALIVE_TIMEOUT=65

# Monitoring and Logging
METRICS_ENABLED=false
PROMETHEUS_PORT=9090
LOG_FILE_PATH=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# Database (Optional)
DATABASE_URL=sqlite:///./chatbot_proxy.db
REDIS_URL=redis://localhost:6379/0

# Development Settings
RELOAD_ON_CHANGE=false
PROFILING_ENABLED=false
