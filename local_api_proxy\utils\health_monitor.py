"""
Universal Chatbot Proxy - Health Monitoring
System health monitoring, alerting, and diagnostics
"""

import asyncio
import logging
import time
import psutil
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum


class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheck:
    """Individual health check result"""
    name: str
    status: HealthStatus
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: float = None
    duration: float = 0.0
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class SystemMetrics:
    """System performance metrics"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_connections: int
    uptime: float
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class HealthMonitor:
    """System health monitoring and alerting"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.health_checks: Dict[str, Callable] = {}
        self.health_history: List[HealthCheck] = []
        self.metrics_history: List[SystemMetrics] = []
        self.alert_callbacks: List[Callable] = []
        
        # Configuration
        self.check_interval = 30  # seconds
        self.history_limit = 1000
        self.alert_thresholds = {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "disk_percent": 90.0,
            "response_time": 5.0
        }
        
        # State
        self.is_monitoring = False
        self.monitor_task = None
        self.start_time = time.time()
        
        # Register default health checks
        self._register_default_checks()
    
    def _register_default_checks(self):
        """Register default system health checks"""
        self.register_health_check("system_resources", self._check_system_resources)
        self.register_health_check("disk_space", self._check_disk_space)
        self.register_health_check("network_connectivity", self._check_network_connectivity)
    
    def register_health_check(self, name: str, check_func: Callable):
        """Register a health check function"""
        self.health_checks[name] = check_func
        self.logger.info(f"Registered health check: {name}")
    
    def register_alert_callback(self, callback: Callable):
        """Register callback for health alerts"""
        self.alert_callbacks.append(callback)
    
    async def start_monitoring(self):
        """Start health monitoring"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("Health monitoring started")
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        self.is_monitoring = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Health monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._run_health_checks()
                await self._collect_metrics()
                await self._check_alerts()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _run_health_checks(self):
        """Run all registered health checks"""
        for name, check_func in self.health_checks.items():
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()
                
                duration = time.time() - start_time
                
                if isinstance(result, HealthCheck):
                    result.duration = duration
                    health_check = result
                else:
                    # Convert simple result to HealthCheck
                    status = HealthStatus.HEALTHY if result else HealthStatus.CRITICAL
                    health_check = HealthCheck(
                        name=name,
                        status=status,
                        message="Check passed" if result else "Check failed",
                        duration=duration
                    )
                
                self._store_health_check(health_check)
                
            except Exception as e:
                error_check = HealthCheck(
                    name=name,
                    status=HealthStatus.CRITICAL,
                    message=f"Health check failed: {str(e)}",
                    details={"error": str(e)}
                )
                self._store_health_check(error_check)
                self.logger.error(f"Health check {name} failed: {e}")
    
    async def _collect_metrics(self):
        """Collect system performance metrics"""
        try:
            metrics = SystemMetrics(
                cpu_percent=psutil.cpu_percent(interval=1),
                memory_percent=psutil.virtual_memory().percent,
                disk_percent=psutil.disk_usage('/').percent,
                network_connections=len(psutil.net_connections()),
                uptime=time.time() - self.start_time
            )
            
            self._store_metrics(metrics)
            
        except Exception as e:
            self.logger.error(f"Failed to collect metrics: {e}")
    
    async def _check_alerts(self):
        """Check for alert conditions"""
        if not self.metrics_history:
            return
        
        latest_metrics = self.metrics_history[-1]
        alerts = []
        
        # Check CPU usage
        if latest_metrics.cpu_percent > self.alert_thresholds["cpu_percent"]:
            alerts.append(f"High CPU usage: {latest_metrics.cpu_percent:.1f}%")
        
        # Check memory usage
        if latest_metrics.memory_percent > self.alert_thresholds["memory_percent"]:
            alerts.append(f"High memory usage: {latest_metrics.memory_percent:.1f}%")
        
        # Check disk usage
        if latest_metrics.disk_percent > self.alert_thresholds["disk_percent"]:
            alerts.append(f"High disk usage: {latest_metrics.disk_percent:.1f}%")
        
        # Check recent health check failures
        recent_failures = [
            check for check in self.health_history[-10:]
            if check.status == HealthStatus.CRITICAL
        ]
        
        if len(recent_failures) > 3:
            alerts.append(f"Multiple health check failures: {len(recent_failures)} in last 10 checks")
        
        # Send alerts
        for alert in alerts:
            await self._send_alert(alert, latest_metrics)
    
    async def _send_alert(self, message: str, metrics: SystemMetrics):
        """Send alert to registered callbacks"""
        alert_data = {
            "message": message,
            "timestamp": time.time(),
            "metrics": metrics,
            "severity": "warning" if "High" in message else "critical"
        }
        
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert_data)
                else:
                    callback(alert_data)
            except Exception as e:
                self.logger.error(f"Alert callback failed: {e}")
    
    def _store_health_check(self, health_check: HealthCheck):
        """Store health check result"""
        self.health_history.append(health_check)
        
        # Trim history
        if len(self.health_history) > self.history_limit:
            self.health_history = self.health_history[-self.history_limit:]
    
    def _store_metrics(self, metrics: SystemMetrics):
        """Store system metrics"""
        self.metrics_history.append(metrics)
        
        # Trim history
        if len(self.metrics_history) > self.history_limit:
            self.metrics_history = self.metrics_history[-self.history_limit:]
    
    async def _check_system_resources(self) -> HealthCheck:
        """Check system resource usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            
            if cpu_percent > 90 or memory_percent > 95:
                status = HealthStatus.CRITICAL
                message = f"Critical resource usage: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%"
            elif cpu_percent > 70 or memory_percent > 80:
                status = HealthStatus.WARNING
                message = f"High resource usage: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Normal resource usage: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%"
            
            return HealthCheck(
                name="system_resources",
                status=status,
                message=message,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_percent,
                    "available_memory": psutil.virtual_memory().available
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="system_resources",
                status=HealthStatus.CRITICAL,
                message=f"Failed to check system resources: {str(e)}"
            )
    
    async def _check_disk_space(self) -> HealthCheck:
        """Check disk space usage"""
        try:
            disk_usage = psutil.disk_usage('/')
            percent_used = (disk_usage.used / disk_usage.total) * 100
            
            if percent_used > 95:
                status = HealthStatus.CRITICAL
                message = f"Critical disk usage: {percent_used:.1f}%"
            elif percent_used > 85:
                status = HealthStatus.WARNING
                message = f"High disk usage: {percent_used:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Normal disk usage: {percent_used:.1f}%"
            
            return HealthCheck(
                name="disk_space",
                status=status,
                message=message,
                details={
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "percent": percent_used
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="disk_space",
                status=HealthStatus.CRITICAL,
                message=f"Failed to check disk space: {str(e)}"
            )
    
    async def _check_network_connectivity(self) -> HealthCheck:
        """Check network connectivity"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get('http://httpbin.org/status/200') as response:
                    if response.status == 200:
                        return HealthCheck(
                            name="network_connectivity",
                            status=HealthStatus.HEALTHY,
                            message="Network connectivity OK"
                        )
                    else:
                        return HealthCheck(
                            name="network_connectivity",
                            status=HealthStatus.WARNING,
                            message=f"Network connectivity issue: HTTP {response.status}"
                        )
        
        except Exception as e:
            return HealthCheck(
                name="network_connectivity",
                status=HealthStatus.CRITICAL,
                message=f"Network connectivity failed: {str(e)}"
            )
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current overall health status"""
        if not self.health_history:
            return {
                "status": HealthStatus.UNKNOWN.value,
                "message": "No health checks performed yet"
            }
        
        # Get latest health checks
        latest_checks = {}
        for check in reversed(self.health_history):
            if check.name not in latest_checks:
                latest_checks[check.name] = check
        
        # Determine overall status
        statuses = [check.status for check in latest_checks.values()]
        
        if HealthStatus.CRITICAL in statuses:
            overall_status = HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            overall_status = HealthStatus.WARNING
        else:
            overall_status = HealthStatus.HEALTHY
        
        # Get latest metrics
        latest_metrics = self.metrics_history[-1] if self.metrics_history else None
        
        return {
            "status": overall_status.value,
            "checks": {name: {
                "status": check.status.value,
                "message": check.message,
                "timestamp": check.timestamp
            } for name, check in latest_checks.items()},
            "metrics": {
                "cpu_percent": latest_metrics.cpu_percent,
                "memory_percent": latest_metrics.memory_percent,
                "disk_percent": latest_metrics.disk_percent,
                "uptime": latest_metrics.uptime
            } if latest_metrics else None,
            "uptime": time.time() - self.start_time
        }
    
    def get_health_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent health check history"""
        recent_checks = self.health_history[-limit:] if limit else self.health_history
        
        return [{
            "name": check.name,
            "status": check.status.value,
            "message": check.message,
            "timestamp": check.timestamp,
            "duration": check.duration,
            "details": check.details
        } for check in recent_checks]


# Global health monitor instance
_health_monitor: Optional[HealthMonitor] = None


def get_health_monitor() -> HealthMonitor:
    """Get global health monitor instance"""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = HealthMonitor()
    return _health_monitor
