{"name": "universal-chatbot-proxy-extension", "version": "1.0.0", "description": "Chrome extension for Universal Chatbot Proxy", "main": "background.js", "scripts": {"build": "webpack --mode=production", "build:dev": "webpack --mode=development", "watch": "webpack --mode=development --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "format": "prettier --write src/**/*.js", "clean": "rimraf dist/", "package": "npm run build && web-ext build --source-dir=dist/", "dev": "npm run build:dev && web-ext run --source-dir=dist/"}, "keywords": ["chrome-extension", "chatbot", "openai", "proxy", "automation"], "author": "Universal Chatbot Proxy Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@types/chrome": "^0.0.248", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "web-ext": "^7.8.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"webextension-polyfill": "^0.10.0"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "collectCoverageFrom": ["src/**/*.js", "!src/**/*.test.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"env": {"browser": true, "es2021": true, "webextensions": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error"}, "globals": {"chrome": "readonly"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}}