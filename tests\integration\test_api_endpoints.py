"""
Universal Chatbot Proxy - API Endpoints Integration Tests
Integration tests for FastAPI endpoints
"""

import pytest
import json
from httpx import Async<PERSON><PERSON>
from unittest.mock import AsyncMock, patch

from local_api_proxy.server import app
from local_api_proxy.models import ChatCompletionRequest, Message


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test health check endpoint."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "version" in data
        assert "uptime" in data
        assert "extension_connected" in data
        assert "active_chatbots" in data
        assert "timestamp" in data


class TestModelsEndpoint:
    """Test models listing endpoint."""
    
    @pytest.mark.asyncio
    async def test_list_models(self):
        """Test listing available models."""
        async with <PERSON>ync<PERSON><PERSON>(app=app, base_url="http://test") as client:
            response = await client.get("/v1/models")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "object" in data
        assert data["object"] == "list"
        assert "data" in data
        assert isinstance(data["data"], list)
        
        # Check model structure
        if data["data"]:
            model = data["data"][0]
            assert "id" in model
            assert "object" in model
            assert model["object"] == "model"
            assert "created" in model
            assert "owned_by" in model
    
    @pytest.mark.asyncio
    async def test_list_models_with_auth(self):
        """Test listing models with authentication."""
        headers = {"Authorization": "Bearer test-key"}
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/v1/models", headers=headers)
        
        # Should work even with auth header when auth is not required
        assert response.status_code == 200


class TestChatCompletionEndpoint:
    """Test chat completion endpoint."""
    
    @pytest.fixture
    def chat_request_data(self):
        """Sample chat completion request data."""
        return {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": "Hello, world!"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
    
    @pytest.mark.asyncio
    async def test_chat_completion_success(self, chat_request_data):
        """Test successful chat completion."""
        with patch('local_api_proxy.server.openai_compat') as mock_compat, \
             patch('local_api_proxy.server.extension_bridge') as mock_bridge:
            
            # Mock successful response
            mock_compat.create_chat_completion.return_value = {
                "id": "chatcmpl-test",
                "object": "chat.completion",
                "created": 1234567890,
                "model": "gpt-3.5-turbo",
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "Hello! How can I help you today?"
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 15,
                    "total_tokens": 25
                }
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post("/v1/chat/completions", json=chat_request_data)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["object"] == "chat.completion"
            assert data["model"] == "gpt-3.5-turbo"
            assert len(data["choices"]) == 1
            assert data["choices"][0]["message"]["role"] == "assistant"
            assert "usage" in data
    
    @pytest.mark.asyncio
    async def test_chat_completion_streaming(self, chat_request_data):
        """Test streaming chat completion."""
        chat_request_data["stream"] = True
        
        with patch('local_api_proxy.server.openai_compat') as mock_compat:
            # Mock streaming response
            async def mock_stream():
                yield "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}\n\n"
                yield "data: {\"choices\":[{\"delta\":{\"content\":\" world\"}}]}\n\n"
                yield "data: [DONE]\n\n"
            
            mock_compat.create_chat_completion_stream.return_value = mock_stream()
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post("/v1/chat/completions", json=chat_request_data)
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/plain; charset=utf-8"
            
            # Check streaming content
            content = response.text
            assert "data: " in content
            assert "[DONE]" in content
    
    @pytest.mark.asyncio
    async def test_chat_completion_invalid_request(self):
        """Test chat completion with invalid request."""
        invalid_data = {
            "model": "gpt-3.5-turbo",
            # Missing required 'messages' field
            "temperature": 0.7
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post("/v1/chat/completions", json=invalid_data)
        
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_chat_completion_service_error(self, chat_request_data):
        """Test chat completion with service error."""
        with patch('local_api_proxy.server.openai_compat') as mock_compat:
            mock_compat.create_chat_completion.side_effect = Exception("Service unavailable")
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post("/v1/chat/completions", json=chat_request_data)
            
            assert response.status_code == 500
            data = response.json()
            assert "error" in data


class TestLegacyCompletionEndpoint:
    """Test legacy completion endpoint."""
    
    @pytest.fixture
    def completion_request_data(self):
        """Sample completion request data."""
        return {
            "model": "gpt-3.5-turbo",
            "prompt": "Hello, world!",
            "max_tokens": 100,
            "temperature": 0.7
        }
    
    @pytest.mark.asyncio
    async def test_completion_success(self, completion_request_data):
        """Test successful text completion."""
        with patch('local_api_proxy.server.openai_compat') as mock_compat:
            # Mock successful response
            mock_compat.create_completion.return_value = {
                "id": "cmpl-test",
                "object": "text_completion",
                "created": 1234567890,
                "model": "gpt-3.5-turbo",
                "choices": [{
                    "index": 0,
                    "text": "Hello! How can I help you today?",
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 15,
                    "total_tokens": 25
                }
            }
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post("/v1/completions", json=completion_request_data)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["object"] == "text_completion"
            assert data["model"] == "gpt-3.5-turbo"
            assert len(data["choices"]) == 1
            assert data["choices"][0]["text"] == "Hello! How can I help you today?"
    
    @pytest.mark.asyncio
    async def test_completion_streaming(self, completion_request_data):
        """Test streaming text completion."""
        completion_request_data["stream"] = True
        
        with patch('local_api_proxy.server.openai_compat') as mock_compat:
            # Mock streaming response
            async def mock_stream():
                yield "data: {\"choices\":[{\"text\":\"Hello\"}]}\n\n"
                yield "data: {\"choices\":[{\"text\":\" world\"}]}\n\n"
                yield "data: [DONE]\n\n"
            
            mock_compat.create_completion_stream.return_value = mock_stream()
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post("/v1/completions", json=completion_request_data)
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/plain; charset=utf-8"


class TestConfigurationEndpoints:
    """Test configuration management endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_configuration(self):
        """Test getting current configuration."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/v1/config")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "chatbots" in data
        assert "default_chatbot" in data
        assert "default_model" in data
        
        # Check chatbot structure
        if data["chatbots"]:
            chatbot_name = list(data["chatbots"].keys())[0]
            chatbot = data["chatbots"][chatbot_name]
            assert "url" in chatbot
            assert "models" in chatbot
            assert "enabled" in chatbot
    
    @pytest.mark.asyncio
    async def test_update_configuration(self):
        """Test updating configuration."""
        config_update = {
            "default_chatbot": "kimi",
            "chatbots": {
                "test": {
                    "url": "https://test.example.com",
                    "enabled": True,
                    "models": ["test-model"]
                }
            }
        }
        
        with patch('local_api_proxy.server.extension_bridge') as mock_bridge:
            mock_bridge.update_config.return_value = None
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.put("/v1/config", json=config_update)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True


class TestErrorHandling:
    """Test error handling in API endpoints."""
    
    @pytest.mark.asyncio
    async def test_404_endpoint(self):
        """Test non-existent endpoint."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/v1/nonexistent")
        
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_method_not_allowed(self):
        """Test method not allowed."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.delete("/v1/health")
        
        assert response.status_code == 405
    
    @pytest.mark.asyncio
    async def test_invalid_json(self):
        """Test invalid JSON in request."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/v1/chat/completions",
                content="invalid json",
                headers={"Content-Type": "application/json"}
            )
        
        assert response.status_code == 422


class TestRateLimiting:
    """Test rate limiting functionality."""
    
    @pytest.mark.asyncio
    async def test_rate_limit_not_exceeded(self, chat_request_data):
        """Test normal request within rate limits."""
        with patch('local_api_proxy.server.rate_limiter') as mock_limiter:
            mock_limiter.is_allowed.return_value = True
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post("/v1/chat/completions", json=chat_request_data)
            
            # Should not be rate limited
            assert response.status_code != 429
    
    @pytest.mark.asyncio
    async def test_rate_limit_exceeded(self, chat_request_data):
        """Test request exceeding rate limits."""
        with patch('local_api_proxy.server.rate_limiter') as mock_limiter:
            mock_limiter.is_allowed.return_value = False
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.post("/v1/chat/completions", json=chat_request_data)
            
            assert response.status_code == 429
            data = response.json()
            assert "error" in data


class TestMetricsEndpoint:
    """Test metrics endpoint."""
    
    @pytest.mark.asyncio
    async def test_metrics_disabled(self):
        """Test metrics endpoint when disabled."""
        with patch('local_api_proxy.server.metrics', None):
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get("/metrics")
            
            assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_metrics_enabled(self):
        """Test metrics endpoint when enabled."""
        with patch('local_api_proxy.server.metrics') as mock_metrics:
            mock_metrics.get_prometheus_metrics.return_value = "# Prometheus metrics\ntest_metric 1.0\n"
            
            async with AsyncClient(app=app, base_url="http://test") as client:
                response = await client.get("/metrics")
            
            assert response.status_code == 200
            assert "test_metric" in response.text
